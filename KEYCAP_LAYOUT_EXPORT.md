# 键帽排列导出功能说明

## 🎯 新功能概述

根据用户需求，新增了键帽单个排列的SVG导出功能，将键帽按照标准顺序单个排开，便于制作和管理。

## 🔧 功能实现

### 1. 键帽排列顺序
按照标准键盘布局，将键帽分为7行排列：

```javascript
const keycapOrder = [
  // 第一行：功能键区
  ['ESC', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'PRT', 'SCR', 'PAU', 'INS'],
  
  // 第二行：数字键区
  ['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'BACK', 'HOME', 'PGUP', '/', '*'],
  
  // 第三行：QWERTY行
  ['TAB', 'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\', 'DEL', 'END', 'PGDN'],
  
  // 第四行：ASDF行
  ['CAPS', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'", 'ENTER', '7', '8', '9', '+'],
  
  // 第五行：ZXCV行
  ['SHIFT', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/', 'SHIFT', '↑', '4', '5', '6'],
  
  // 第六行：控制键行
  ['CTRL', 'WIN', 'ALT', 'SPACE', 'ALT', 'FN', 'MENU', 'CTRL', '←', '↓', '→', '1', '2', '3', 'ENT'],
  
  // 第七行：小键盘底部
  ['0', '.']
];
```

### 2. SVG生成参数
- **键帽尺寸**：60×60px
- **键帽间距**：10px
- **边距**：20px
- **背景色**：#f8f9fa

### 3. 键帽渲染
每个键帽包含：
- **背景矩形**：白色背景，灰色边框，圆角8px
- **自定义图片**：如果键帽有背景图片，会嵌入显示
- **键帽标签**：显示键帽名称，字体大小根据标签长度调整

## 🎨 导出按钮

### 位置1：顶部操作栏
```
[💾 保存到云端] [📄 导出布局SVG] [🔲 导出键帽排列]
```

### 位置2：左侧工具栏
```
💾 (保存到云端)
📄 (导出布局SVG)
🔲 (导出键帽排列)
👁️ (3D预览)
```

## 📊 功能对比

| 功能 | 导出布局SVG | 导出键帽排列 |
|------|-------------|--------------|
| 排列方式 | 键盘布局 | 单个排开 |
| 用途 | 设计预览 | 制作生产 |
| 包含内容 | 完整设计 | 仅键帽 |
| 文件名后缀 | `.svg` | `_键帽排列.svg` |
| 图片处理 | 完全嵌入 | 完全嵌入 |

## 🔍 技术细节

### 键帽ID映射
```javascript
const labelToIdMap = {
  'ESC': 'key-esc',
  'F1': 'key-f1', 'F2': 'key-f2', // ...
  'SPACE': 'key-space',
  'ENTER': 'key-enter',
  // ... 完整映射表
};
```

### 图片处理
- **自动转换**：所有键帽背景图片自动转换为base64格式
- **完全嵌入**：导出的SVG文件完全独立，包含所有图片数据
- **透明度支持**：保持键帽自定义的透明度设置

### 文件命名
```javascript
const fileName = designName.trim() || `${layoutName}键帽排列_${timestamp}`;
link.download = `${fileName}_键帽排列.svg`;
```

## ✅ 使用流程

1. **设计键帽**：在设计器中为键帽添加背景图片
2. **点击导出**：选择"🔲 导出键帽排列"按钮
3. **自动处理**：系统自动转换图片并生成SVG
4. **下载文件**：获得包含所有键帽的排列SVG文件

## 🎯 预期效果

导出的SVG文件将显示：
- ✅ 所有键帽按行整齐排列
- ✅ 每个键帽显示对应的标签
- ✅ 自定义背景图片完全嵌入
- ✅ 文件完全独立，可直接分享
- ✅ 适合制作商使用的标准格式

## 📋 导出统计信息

导出完成后会显示：
- **布局类型**：108键/87键等
- **自定义键帽数量**：有背景图片的键帽数
- **嵌入图片数量**：转换的图片总数
- **文件大小**：生成的SVG文件大小
- **排列方式**：单个键帽按行排列

这个新功能完美满足了用户将键帽单个排开的需求，便于制作和生产管理！
