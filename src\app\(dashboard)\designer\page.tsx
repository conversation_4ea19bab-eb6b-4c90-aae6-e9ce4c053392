'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import Link from 'next/link';
import dynamic from 'next/dynamic';

// 动态导入Three.js组件以避免SSR问题
const ThreeJSKeyboard = dynamic(() => import('../../../components/designer/ThreeJSKeyboard'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
    <div className="text-gray-500">加载3D键盘中...</div>
  </div>
});



// 高性能拖拽预览组件
const DragPreviewComponent = React.memo(({ dragPreview }: { dragPreview: any }) => {
  // 使用 useMemo 缓存样式计算，避免每次渲染都重新计算
  const previewStyle = useMemo(() => ({
    left: `${dragPreview.mouseX}px`,
    top: `${dragPreview.mouseY}px`,
    transform: 'translate(-50%, -50%)',
    // 高性能优化属性
    willChange: 'transform', // 告诉浏览器这个元素会频繁变化
    backfaceVisibility: 'hidden' as const, // 避免背面渲染
    transformStyle: 'preserve-3d' as const, // 启用3D渲染上下文
    contain: 'layout style paint' as const, // 限制重排重绘范围
    isolation: 'isolate' as const, // 创建新的堆叠上下文
  }), [dragPreview.mouseX, dragPreview.mouseY]);

  const imageStyle = useMemo(() => ({
    // 使用预先计算好的显示比例，确保与SVG中的尺寸完全一致
    width: `${dragPreview.previewWidth * dragPreview.displayScaleX}px`,
    height: `${dragPreview.previewHeight * dragPreview.displayScaleY}px`,
    filter: 'drop-shadow(0 12px 24px rgba(59, 130, 246, 0.4))',
    // 优化图片渲染
    imageRendering: 'crisp-edges' as const,
    // 性能优化
    transform: 'translateZ(0)', // 硬件加速
    backfaceVisibility: 'hidden' as const,
    willChange: 'auto' as const,
    // 防止拖拽时图片闪烁
    userSelect: 'none' as const,
    WebkitUserSelect: 'none' as const,
    pointerEvents: 'none' as const,
  }), [dragPreview.previewWidth, dragPreview.previewHeight, dragPreview.displayScaleX, dragPreview.displayScaleY]);

  return (
    <div
      className="fixed z-[9999] pointer-events-none"
      style={previewStyle}
    >
      <div 
        className="relative"
        style={{
          // 优化渲染性能
          transform: 'translateZ(0)', // 强制硬件加速
          willChange: 'auto', // 让浏览器自动决定
        }}
      >
        {/* 拖拽预览图片 */}
        <img
          src={dragPreview.imageUrl}
          alt={dragPreview.imageName}
          className="object-contain opacity-85 shadow-2xl border-2 border-blue-400 border-dashed rounded-lg bg-white bg-opacity-95"
          style={imageStyle}
        />
        
        {/* 图片信息提示 */}
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-blue-600 bg-opacity-90 text-white text-xs px-3 py-1 rounded-full whitespace-nowrap font-medium shadow-lg">
          📎 {dragPreview.imageName} • SVG尺寸: {Math.round(dragPreview.previewWidth)}×{Math.round(dragPreview.previewHeight)} (1:1显示)
        </div>
        
        {/* 放置指示器 - 发光效果 */}
        <div className="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-60">
          <div className="absolute inset-0 border border-blue-300 rounded-lg animate-ping"></div>
        </div>
        
        {/* 十字准星 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 pointer-events-none">
          <div className="absolute top-1/2 left-0 w-full h-0.5 bg-blue-500 transform -translate-y-1/2"></div>
          <div className="absolute left-1/2 top-0 w-0.5 h-full bg-blue-500 transform -translate-x-1/2"></div>
        </div>
      </div>
    </div>
  );
});

// 键盘布局选项 - 简化为三个主要选项
const KEYBOARD_LAYOUTS = [
  { id: '108', name: '原厂108键', keys: 108, description: '全尺寸键盘' },
  { id: '87', name: '87键', keys: 87, description: '无数字键盘' },
  { id: '87-side', name: '87键侧刻', keys: 87, description: '87键侧刻版本' },
];

// 素材分类定义 - 简化为只有全部分类
const MATERIAL_CATEGORIES = [
  { id: 'all', name: '全部', icon: '📁', description: '所有素材' },
];

// 移除键帽高度选项 - 不再需要键标选择

interface DesignSettings {
  layout: string;
  customWidth: number;
  customHeight: number;
}

interface UploadedImage {
  id: string;
  file: File | null;
  url: string;
  name: string;
  serverPath?: string;
  materialId?: string;
  thumbnailPath?: string;
  width?: number;
  height?: number;
  fileSize?: number;
  createTime?: string;
  category?: string;
  tags?: string[];
}

interface DraggedElement {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  imageUrl: string;
  opacity: number; // 透明度 0-1
  borderRadius: number; // 圆角 0-20
  rotation: number; // 旋转角度 0-360
}

// 键帽个性化设置接口
interface KeycapCustomization {
  keyId: string;
  textColor: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: string;
  textStyle: string; // 'normal' | 'italic' | 'oblique'
  textDecoration: string; // 'none' | 'underline' | 'line-through'
  backgroundImage?: string; // 背景贴图URL
  backgroundOpacity: number; // 背景透明度
  backgroundScale: number; // 背景缩放
  backgroundPosition: { x: number; y: number }; // 背景位置偏移
  coverageMode: 'top-only' | 'full-keycap'; // 新增：图像覆盖模式
}

export default function KeycapDesigner() {
  const [currentView, setCurrentView] = useState<'design' | 'preview'>('design');
  const [renderMode, setRenderMode] = useState<'2d' | '3d'>('2d'); // 新增渲染模式状态
  const [designSettings, setDesignSettings] = useState<DesignSettings>({
    layout: '108',
    customWidth: 1320,
    customHeight: 450,
  });
  
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [draggedElements, setDraggedElements] = useState<DraggedElement[]>([]);
  const [designName, setDesignName] = useState('未命名的设计');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]); // 选中的键帽ID列表
  const [showGuide, setShowGuide] = useState(true); // 控制引导提示的显示
  const [showWelcome, setShowWelcome] = useState(true); // 控制欢迎界面的显示
  const [selectedElement, setSelectedElement] = useState<string | null>(null); // 选中的拖拽元素ID
  const [isDragOver, setIsDragOver] = useState(false); // 跟踪拖拽悬停状态
  
  // 拖拽预览状态
  // 使用 useRef 直接操作DOM，避免React状态更新的性能开销
  const dragPreviewRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);

  // 超高性能拖拽预览组件 - 直接DOM操作版本
  const UltraFastDragPreview = React.memo(() => {
    return (
      <div
        ref={dragPreviewRef}
        className="fixed z-[9999] pointer-events-none opacity-0"
        style={{
          left: '-9999px',
          top: '-9999px',
          transform: 'translate(-50%, -50%)',
          willChange: 'transform, opacity',
          backfaceVisibility: 'hidden',
          transformStyle: 'preserve-3d',
          contain: 'layout style paint',
          isolation: 'isolate',
          transition: 'none', // 禁用所有CSS过渡
        }}
      >
        <div 
          className="relative"
          style={{
            transform: 'translateZ(0)',
            willChange: 'auto',
          }}
        >
          {/* 预览图片容器 */}
          <img
            className="object-contain shadow-2xl border-2 border-blue-400 border-dashed rounded-lg bg-white bg-opacity-95"
            style={{
              imageRendering: 'crisp-edges',
              transform: 'translateZ(0)',
              backfaceVisibility: 'hidden',
              willChange: 'auto',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              pointerEvents: 'none',
              filter: 'drop-shadow(0 12px 24px rgba(59, 130, 246, 0.4))',
            }}
          />
          
          {/* 图片信息提示 */}
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-blue-600 bg-opacity-90 text-white text-xs px-3 py-1 rounded-full whitespace-nowrap font-medium shadow-lg">
            <span>📎 拖拽中... • 1:1显示</span>
          </div>
          
          {/* 放置指示器 */}
          <div className="absolute inset-0 border-2 border-blue-500 rounded-lg opacity-60">
            <div className="absolute inset-0 border border-blue-300 rounded-lg animate-ping"></div>
          </div>
          
          {/* 十字准星 */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 pointer-events-none">
            <div className="absolute top-1/2 left-0 w-full h-0.5 bg-blue-500 transform -translate-y-1/2"></div>
            <div className="absolute left-1/2 top-0 w-0.5 h-full bg-blue-500 transform -translate-x-1/2"></div>
          </div>
        </div>
      </div>
    );
  });
  
  const [dragPreview, setDragPreview] = useState<{
    isActive: boolean;
    imageUrl: string;
    imageName: string;
    mouseX: number;
    mouseY: number;
    naturalWidth: number;
    naturalHeight: number;
    previewWidth: number;
    previewHeight: number;
    // 添加显示比例信息
    displayScaleX: number;
    displayScaleY: number;
  } | null>(null);
  
  // 键帽个性化设置状态
  const [keycapCustomizations, setKeycapCustomizations] = useState<Record<string, KeycapCustomization>>({});
  const [selectedKeycap, setSelectedKeycap] = useState<string | null>(null); // 当前编辑的键帽ID
  const [editingMode, setEditingMode] = useState<'image' | 'keycap' | null>(null); // 编辑模式
  
  // 素材库相关状态
  const [materialLibraryView, setMaterialLibraryView] = useState<'categories' | 'materials'>('categories'); // 素材库视图模式
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null); // 选中的分类
  const [publicMaterials, setPublicMaterials] = useState<UploadedImage[]>([]); // 公共素材
  const [filteredMaterials, setFilteredMaterials] = useState<UploadedImage[]>([]); // 过滤后的素材
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const designAreaRef = useRef<HTMLDivElement>(null);

  // 添加加载状态
  const [isUploading, setIsUploading] = useState(false);

  // 添加保存状态
  const [isSaving, setIsSaving] = useState(false);

  // 添加状态来管理当前编辑的设计
  const [currentDesignId, setCurrentDesignId] = useState<string | null>(null);
  const [isLoadingDesign, setIsLoadingDesign] = useState(false);
  const [pendingDesignElements, setPendingDesignElements] = useState<any[]>([]);

  // 页面加载时获取用户的图片素材
  useEffect(() => {
    loadUserImages();
    loadPublicMaterials();
    loadTestImage(); // 加载测试图片
  }, []);

  // 加载测试图片用于前端测试
  const loadTestImage = () => {
    const testImage: UploadedImage = {
      id: 'test-image-001',
      file: null, // 测试图片没有原始File对象
      url: '/test.jpg', // 使用public目录下的test.jpg
      name: '测试图片.jpg',
      serverPath: '/test.jpg',
      materialId: 'test-material-001',
      thumbnailPath: '/test.jpg',
      width: 800, // 假设的尺寸
      height: 600,
      fileSize: 150000, // 假设的文件大小 (约150KB)
      createTime: new Date().toISOString(),
      category: 'test',
      tags: ['测试', '前端调试']
    };

    // 将测试图片添加到uploadedImages状态中
    setUploadedImages(prev => {
      // 检查是否已经存在测试图片，避免重复添加
      const existingTestImage = prev.find(img => img.id === 'test-image-001');
      if (existingTestImage) {
        console.log('⚠️ 测试图片已存在，跳过重复添加');
        return prev;
      }
      console.log('✅ 测试图片已加载到设计器界面', testImage);
      return [testImage, ...prev]; // 将测试图片放在最前面
    });

    console.log('🔍 测试图片加载完成，URL:', testImage.url);
  };

  // 获取公共素材库的素材
  const loadPublicMaterials = async () => {
    try {
      const token = localStorage.getItem('token');
      console.log('🔍 开始加载公共素材库');
      
      if (!token) {
        console.log('❌ 用户未登录，跳过公共素材加载');
        return;
      }

      console.log('📡 发起公共素材加载请求: http://localhost:8080/api/materials/public');
      const response = await fetch('http://localhost:8080/api/materials/public', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 公共素材加载响应状态:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          console.log('❌ 用户认证失败，跳过公共素材加载');
          return;
        }
        console.log('❌ HTTP错误:', response.status, response.statusText);
        return; // 不抛出错误，静默失败
      }

      const result = await response.json();
      console.log('📋 获取公共素材API响应:', JSON.stringify(result, null, 2));
      
      // 兼容多种响应格式
      let materials = [];
      if (result.code === 200 && result.data) {
        // 处理分页数据结构
        if (result.data.content && Array.isArray(result.data.content)) {
          materials = result.data.content;
        } else if (Array.isArray(result.data)) {
          materials = result.data;
        } else {
          materials = [result.data];
        }
      } else if (result.success && result.data) {
        materials = result.data;
      } else if (Array.isArray(result)) {
        materials = result;
      }
      
      if (materials && materials.length > 0) {
        console.log('🔄 获取到公共素材数量:', materials.length);
        console.log('🔍 原始素材数据:', materials);
        console.log('🔍 第一个素材的详细信息:', materials[0]);
        
        // 转换公共素材数据格式
        const convertedMaterials = materials.map((material: any) => {
          // 构建完整的图片URL - 根据实际API返回的字段
          let imageUrl = '';
          if (material.imageUrl) {
            imageUrl = material.imageUrl.startsWith('http') ? material.imageUrl : `http://localhost:8080${material.imageUrl}`;
          } else if (material.filePath) {
            imageUrl = material.filePath.startsWith('http') ? material.filePath : `http://localhost:8080${material.filePath}`;
          } else if (material.url) {
            imageUrl = material.url;
          }
          
          return {
            id: material.materialId || material.id || `public-${Date.now()}-${Math.random()}`,
            file: null, // 公共素材没有本地文件对象
            url: imageUrl,
            name: material.title || material.name || material.materialName || '未命名素材',
            serverPath: material.filePath || material.imageUrl,
            materialId: material.materialId || material.id,
            thumbnailPath: material.thumbnailUrl || material.thumbnailPath,
            width: material.width,
            height: material.height,
            fileSize: material.fileSize,
            createTime: material.createTime || material.uploadTime,
            category: material.category || material.type || 'all', // 默认分类为all，这样"全部"能显示所有素材
            tags: material.tags || [] // 添加标签信息
          };
        });
        setPublicMaterials(convertedMaterials);
        console.log('✅ 公共素材数据已存储:', convertedMaterials.length, '个');
        console.log('🔍 转换后的素材数据:', convertedMaterials);
      } else {
        console.log('⚠️ 没有获取到公共素材数据');
      }
      
    } catch (error) {
      console.error('❌ 加载公共素材失败:', error);
      // 静默失败，不影响用户体验
      setPublicMaterials([]);
    }
  };

  // 从后端获取用户的图片素材
  const loadUserImages = async () => {
    try {
      const token = localStorage.getItem('token');
      console.log('🔍 开始加载用户素材，token存在:', !!token);
      
      if (!token) {
        console.log('❌ 用户未登录，跳过素材加载');
        return;
      }

      console.log('📡 发起素材加载请求: http://localhost:8080/api/materials');
      const response = await fetch('http://localhost:8080/api/materials', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 素材加载响应状态:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          console.log('❌ 用户认证失败，跳过素材加载');
          return;
        } else if (response.status === 403) {
          console.log('❌ 权限被拒绝(403)，检查后端安全配置');
          // 尝试获取响应详情
          try {
            const errorText = await response.text();
            console.log('❌ 403错误详情:', errorText);
          } catch (e) {
            console.log('❌ 无法获取403错误详情');
          }
          return;
        }
        console.log('❌ HTTP错误:', response.status, response.statusText);
        throw new Error(`获取素材失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 获取素材API完整响应:', JSON.stringify(result, null, 2));
      
      // 兼容多种响应格式
      let materials = [];
      if (result.code === 200 && result.data) {
        materials = result.data;
        console.log('✅ 使用 result.code === 200 格式，素材数量:', materials.length);
      } else if (result.success && result.data) {
        materials = result.data;
        console.log('✅ 使用 result.success 格式，素材数量:', materials.length);
      } else if (Array.isArray(result)) {
        materials = result;
        console.log('✅ 使用 Array 格式，素材数量:', materials.length);
      } else {
        console.log('❌ 未识别的响应格式');
      }
      
      if (materials && materials.length > 0) {
        console.log('🔄 开始转换素材数据:', materials);
        
        // 转换后端数据格式为前端需要的格式
        const convertedMaterials = materials.map((material: any) => {
          console.log('🔄 转换单个素材:', material);
          return {
            id: material.materialId,
            file: null, // 后端加载的图片没有原始File对象
            url: material.fileUrl,
            name: material.fileName,
            serverPath: material.filePath,
            materialId: material.materialId,
            width: material.width,
            height: material.height,
            fileSize: material.fileSize,
            createTime: material.createTime
          };
        });
        
        console.log('✅ 素材转换完成:', convertedMaterials);
        setUploadedImages(convertedMaterials);
        console.log('🎉 从数据库加载用户素材成功，数量：', convertedMaterials.length);
      } else {
        console.log('📭 没有找到用户素材，设置为空数组');
        setUploadedImages([]);
      }
    } catch (error) {
      console.error('💥 加载用户图片失败:', error);
      console.error('💥 错误详情:', error instanceof Error ? error.message : '未知错误');
    }
  };

  // 添加定时器控制引导提示
  useEffect(() => {
    if (uploadedImages.length > 0 && draggedElements.length === 0 && showGuide) {
      const timer = setTimeout(() => {
        setShowGuide(false);
      }, 3000); // 3秒后自动隐藏
      
      return () => clearTimeout(timer);
    }
  }, [uploadedImages.length, draggedElements.length, showGuide]);

  // 当有新的拖拽元素时，重新显示引导
  useEffect(() => {
    if (draggedElements.length > 0) {
      setShowGuide(false);
    }
  }, [draggedElements.length]);

  // 当上传图片时，关闭欢迎界面
  useEffect(() => {
    if (uploadedImages.length > 0) {
      setShowWelcome(false);
    }
  }, [uploadedImages.length]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedElement && (e.key === 'Delete' || e.key === 'Backspace')) {
        e.preventDefault();
        setDraggedElements(prev => prev.filter(el => el.id !== selectedElement));
        setSelectedElement(null);
      }
      
      if (e.key === 'Escape') {
        setSelectedElement(null);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedElement]);

  // 添加键帽动画样式
  useEffect(() => {
    const styleId = 'keycap-animations';
    const existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* 键盘模板静态样式 - 移除所有动画效果 */
        .keycap-group {
          cursor: pointer;
        }
        
        .keycap-top {
          /* 移除过渡效果 */
        }
        
        .keycap-top.selected {
          fill: rgba(59, 130, 246, 0.3) !important;
          stroke: rgba(59, 130, 246, 0.7) !important;
          stroke-width: 2 !important;
        }
        
        /* 现代化滑块样式 */
        .range-slider {
          -webkit-appearance: none;
          appearance: none;
          height: 6px;
          border-radius: 3px;
          background: #e5e7eb;
          outline: none;
        }
        
        .range-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          transition: all 0.2s ease;
        }
        
        .range-slider::-webkit-slider-thumb:hover {
          background: #2563eb;
          transform: scale(1.1);
        }
        
        .range-slider::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        /* 拖拽元素样式 */
        .drag-element {
          transition: transform 0.1s ease-out, box-shadow 0.2s ease;
        }
        
        .drag-element:hover {
          transform: scale(1.02);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .drag-element:active {
          transform: scale(0.98);
        }
        
        /* 选择边框动画 */
        @keyframes dash {
          0% {
            stroke-dashoffset: 0;
          }
          100% {
            stroke-dashoffset: 20;
          }
        }
      `;
      document.head.appendChild(style);
    }
    
    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // 真实API上传图片
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const token = localStorage.getItem('token');
    if (!token) {
      alert('请先登录后再上传图片');
      return;
    }

    setIsUploading(true);
    
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'WORK'); // 使用WORK类型表示设计作品素材

        // 调用后端上传接口
        const response = await fetch('http://localhost:8080/api/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`上传失败: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (result.code === 200) {
          // 构造图片URL（后端返回完整URL）
          const imageUrl = result.data.fileUrl;
          
          return {
            id: result.data.materialId || (Date.now().toString() + Math.random().toString(36).substr(2, 9)),
            file,
            url: imageUrl,
            name: file.name,
            serverPath: result.data.filePath,
            materialId: result.data.materialId,
            thumbnailPath: result.data.thumbnailPath
          };
        } else {
          throw new Error(result.message || '上传失败');
        }
      });

      // 等待所有上传完成
      const newImages = await Promise.all(uploadPromises);
      
      console.log('图片上传成功:', newImages);
      
      // 重新从数据库加载用户素材，确保数据一致性
      await loadUserImages();
      
      // 上传图片后显示拖拽引导
      setShowGuide(true);
      
    } catch (error) {
      console.error('图片上传失败:', error);
      alert(`图片上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsUploading(false);
      // 清空input，允许重复上传同一文件
      event.target.value = '';
    }
  };

  const handleDragStart = (e: React.MouseEvent, image: UploadedImage) => {
    e.preventDefault();
    e.stopPropagation();
    
    // 获取图片的自然尺寸
    const img = new Image();
    img.onload = () => {
      console.log('🚀 开始超高性能拖拽:', image.name, '尺寸:', img.naturalWidth, 'x', img.naturalHeight);
      
      // 计算预览图片的实际尺寸（与最终SVG中的尺寸一致）
      const maxWidth = 675; // 键盘宽度的一半
      const maxHeight = 210; // 键盘高度的一半
      
      let previewWidth = img.naturalWidth;
      let previewHeight = img.naturalHeight;
      
      // 只有当图片确实超出限制时才缩放，严格保持宽高比
      if (previewWidth > maxWidth || previewHeight > maxHeight) {
        const widthRatio = maxWidth / previewWidth;
        const heightRatio = maxHeight / previewHeight;
        const ratio = Math.min(widthRatio, heightRatio);
        
        previewWidth = previewWidth * ratio;
        previewHeight = previewHeight * ratio;
      }
      
      // 计算当前设计区域的实际显示比例
      let displayScaleX = 1;
      let displayScaleY = 1;
      
      if (designAreaRef.current) {
        const designRect = designAreaRef.current.getBoundingClientRect();
        const svgWidth = 1350;
        const svgHeight = 420;
        const svgAspectRatio = svgWidth / svgHeight;
        const areaAspectRatio = designRect.width / designRect.height;
        
        let actualSvgWidth, actualSvgHeight;
        if (areaAspectRatio > svgAspectRatio) {
          actualSvgHeight = designRect.height;
          actualSvgWidth = actualSvgHeight * svgAspectRatio;
        } else {
          actualSvgWidth = designRect.width;
          actualSvgHeight = actualSvgWidth / svgAspectRatio;
        }
        
        displayScaleX = actualSvgWidth / svgWidth;
        displayScaleY = actualSvgHeight / svgHeight;
      }
      
      // 直接操作DOM设置预览
      const previewElement = dragPreviewRef.current;
      if (previewElement) {
        const imgElement = previewElement.querySelector('img') as HTMLImageElement;
        const infoElement = previewElement.querySelector('span') as HTMLSpanElement;
        
        // 设置图片
        if (imgElement) {
          imgElement.src = image.url;
          imgElement.alt = image.name;
          imgElement.style.width = `${previewWidth * displayScaleX}px`;
          imgElement.style.height = `${previewHeight * displayScaleY}px`;
          imgElement.style.opacity = '0.85';
        }
        
        // 设置信息
        if (infoElement) {
          infoElement.textContent = `📎 ${image.name} • SVG尺寸: ${Math.round(previewWidth)}×${Math.round(previewHeight)} (1:1显示)`;
        }
        
        // 显示预览元素
        previewElement.style.opacity = '1';
        previewElement.style.left = `${e.clientX}px`;
        previewElement.style.top = `${e.clientY}px`;
        
        isDraggingRef.current = true;
      }
      
      // 超高性能的鼠标移动处理 - 直接操作DOM，无React状态更新
      const handleGlobalMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current || !previewElement) return;
        
        // 直接更新DOM位置，无任何延迟
        previewElement.style.left = `${e.clientX}px`;
        previewElement.style.top = `${e.clientY}px`;
      };
      
      // 优化的全局鼠标释放监听
      const handleGlobalMouseUp = (e: MouseEvent) => {
        isDraggingRef.current = false;
        
        // 隐藏预览元素
        if (previewElement) {
          previewElement.style.opacity = '0';
          previewElement.style.left = '-9999px';
          previewElement.style.top = '-9999px';
        }
        
        // 检查是否在设计区域内释放
        const designArea = designAreaRef.current;
        if (designArea) {
          const rect = designArea.getBoundingClientRect();
          const isInDesignArea = e.clientX >= rect.left && e.clientX <= rect.right && 
                                e.clientY >= rect.top && e.clientY <= rect.bottom;
          
          if (isInDesignArea) {
            // 在设计区域内释放，创建图片元素
            handleDropAtPosition(e.clientX, e.clientY, image, img.naturalWidth, img.naturalHeight);
          }
        }
        
        // 移除全局监听器
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
      
      // 添加全局监听器
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
    };
    
    img.src = image.url;
  };

  // 在指定位置放置图片
  const handleDropAtPosition = (clientX: number, clientY: number, image: UploadedImage, naturalWidth: number, naturalHeight: number) => {
    if (!designAreaRef.current) return;
    
    const designRect = designAreaRef.current.getBoundingClientRect();
    const relativeX = clientX - designRect.left;
    const relativeY = clientY - designRect.top;
    
    // SVG坐标系转换
    const svgWidth = 1350;
    const svgHeight = 420;
    const svgAspectRatio = svgWidth / svgHeight;
    const areaAspectRatio = designRect.width / designRect.height;
    
    let actualSvgWidth, actualSvgHeight, offsetX = 0, offsetY = 0;
    
    if (areaAspectRatio > svgAspectRatio) {
      actualSvgHeight = designRect.height;
      actualSvgWidth = actualSvgHeight * svgAspectRatio;
      offsetX = (designRect.width - actualSvgWidth) / 2;
    } else {
      actualSvgWidth = designRect.width;
      actualSvgHeight = actualSvgWidth / svgAspectRatio;
      offsetY = (designRect.height - actualSvgHeight) / 2;
    }
    
    const svgX = ((relativeX - offsetX) / actualSvgWidth) * svgWidth;
    const svgY = ((relativeY - offsetY) / actualSvgHeight) * svgHeight;
    
    // 计算图片尺寸
    const maxWidth = 675;
    const maxHeight = 210;
    let newWidth = naturalWidth;
    let newHeight = naturalHeight;
    
    if (newWidth > maxWidth || newHeight > maxHeight) {
      const ratio = Math.min(maxWidth / newWidth, maxHeight / newHeight);
      newWidth = newWidth * ratio;
      newHeight = newHeight * ratio;
    }
    
    // 以鼠标位置为中心放置
    const elementX = svgX - newWidth / 2;
    const elementY = svgY - newHeight / 2;
    
    const finalX = Math.max(-newWidth/4, Math.min(elementX, svgWidth - newWidth*3/4));
    const finalY = Math.max(-newHeight/4, Math.min(elementY, svgHeight - newHeight*3/4));
    
    const newElement: DraggedElement = {
      id: Date.now().toString(),
      x: finalX,
      y: finalY,
      width: newWidth,
      height: newHeight,
      imageUrl: image.url,
      opacity: 0.9,
      borderRadius: 0,
      rotation: 0
    };
    
    setDraggedElements(prev => [...prev, newElement]);
    setSelectedElement(newElement.id);
    setEditingMode('image');
    setSelectedKeycap(null);
  };

  // 处理拖拽到键帽的功能
  const handleKeycapDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleKeycapDrop = (e: React.DragEvent, keyId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const imageUrl = e.dataTransfer.getData('image/url');
      const imageId = e.dataTransfer.getData('image/id');
      const imageName = e.dataTransfer.getData('image/name');
      
      if (imageUrl && imageId) {
        console.log('🎯 将图片应用到键帽:', keyId, imageName);
        applyImageToKeycap(keyId, imageUrl);
        
        // 自动选择该键帽进行编辑
        setSelectedKeycap(keyId);
        setEditingMode('keycap');
        
        // 显示成功提示
        const keyName = keyId.replace('key-', '').replace('-', ' ').toUpperCase();
        console.log(`✅ 图片 "${imageName}" 已应用到 ${keyName} 键`);
      }
    } catch (error) {
      console.error('❌ 应用图片到键帽失败:', error);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false); // 重置拖拽悬停状态
    const imageId = e.dataTransfer.getData('image/id');
    const imageUrl = e.dataTransfer.getData('image/url');
    
    if (imageId && imageUrl && designAreaRef.current) {
      const rect = designAreaRef.current.getBoundingClientRect();
      
      // 获取图片的实际尺寸
      const img = new Image();
      img.onload = () => {
        // 保持图片原始宽高比，只在必要时缩放
        const maxWidth = 675; // 键盘宽度的一半
        const maxHeight = 210; // 键盘高度的一半
        
        let newWidth = img.naturalWidth;
        let newHeight = img.naturalHeight;
        
        console.log('🖼️ 图片原始尺寸:', { 
          width: newWidth, 
          height: newHeight,
          aspectRatio: (newWidth / newHeight).toFixed(3)
        });
        
        // 只有当图片确实超出限制时才缩放，严格保持宽高比
        if (newWidth > maxWidth || newHeight > maxHeight) {
          const widthRatio = maxWidth / newWidth;
          const heightRatio = maxHeight / newHeight;
          const ratio = Math.min(widthRatio, heightRatio);
          
          console.log('🔄 需要缩放:', { 
            widthRatio: widthRatio.toFixed(3), 
            heightRatio: heightRatio.toFixed(3), 
            finalRatio: ratio.toFixed(3)
          });
          
          // 严格按比例缩放，保持宽高比
          newWidth = newWidth * ratio;
          newHeight = newHeight * ratio;
        }
        
        console.log('📐 最终显示尺寸:', { 
          width: Math.round(newWidth), 
          height: Math.round(newHeight),
          finalAspectRatio: (newWidth / newHeight).toFixed(3)
        });
        
        // 创建保持真实比例的图片元素
        const newElement: DraggedElement = {
          id: Date.now().toString(),
          x: (1350 - newWidth) / 2, // 居中放置
          y: (420 - newHeight) / 2,
          width: newWidth,  // 保持计算出的真实宽度
          height: newHeight, // 保持计算出的真实高度
          imageUrl,
          opacity: 0.9, // 90%透明度，保持清晰度
          borderRadius: 0,
          rotation: 0
        };
        
        setDraggedElements(prev => [...prev, newElement]);
        setSelectedElement(newElement.id); // 自动选中新创建的元素
        setEditingMode('image'); // 设置为图片编辑模式
        setSelectedKeycap(null); // 取消键帽选择
      };
      
      img.src = imageUrl;
    }
  };

  // 拖拽元素移动处理
  const handleElementDrag = (elementId: string, e: React.MouseEvent) => {
    const startX = e.clientX;
    const startY = e.clientY;
    const element = draggedElements.find(el => el.id === elementId);
    
    if (!element || !designAreaRef.current) return;
    
    const startElementX = element.x;
    const startElementY = element.y;
    const designRect = designAreaRef.current.getBoundingClientRect();
    
    // SVG的原始尺寸
    const svgWidth = 1350;
    const svgHeight = 420;
    const svgAspectRatio = svgWidth / svgHeight;
    
    // 设计区域的实际比例
    const areaAspectRatio = designRect.width / designRect.height;
    
    // 预计算缩放比例，避免每次mousemove都计算
    let scaleX, scaleY;
    
    if (areaAspectRatio > svgAspectRatio) {
      // 设计区域更宽，SVG会被高度限制
      const actualSvgHeight = designRect.height;
      const actualSvgWidth = actualSvgHeight * svgAspectRatio;
      scaleX = svgWidth / actualSvgWidth;
      scaleY = svgHeight / actualSvgHeight;
    } else {
      // 设计区域更高，SVG会被宽度限制
      const actualSvgWidth = designRect.width;
      const actualSvgHeight = actualSvgWidth / svgAspectRatio;
      scaleX = svgWidth / actualSvgWidth;
      scaleY = svgHeight / actualSvgHeight;
    }
    
    // 使用 requestAnimationFrame 优化性能和节流
    let rafId: number | null = null;
    let lastUpdateTime = 0;
    const THROTTLE_MS = 8; // 控制更新频率，约120fps
    
    const handleMouseMove = (e: MouseEvent) => {
      const now = Date.now();
      
      // 如果已经有待执行的动画帧，取消它
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
      
      // 节流处理，避免过于频繁的更新
      if (now - lastUpdateTime < THROTTLE_MS) {
        rafId = requestAnimationFrame(() => handleMouseMove(e));
        return;
      }
      
      rafId = requestAnimationFrame(() => {
        const deltaX = (e.clientX - startX) * scaleX;
        const deltaY = (e.clientY - startY) * scaleY;
        
        const newX = Math.max(-element.width/2, Math.min(startElementX + deltaX, 1350 - element.width/2));
        const newY = Math.max(-element.height/2, Math.min(startElementY + deltaY, 420 - element.height/2));
        
        setDraggedElements(prev => prev.map(el => 
          el.id === elementId 
            ? { ...el, x: newX, y: newY }
            : el
        ));
        
        lastUpdateTime = now;
        rafId = null;
      });
    };
    
    const handleMouseUp = () => {
      if (rafId) {
        cancelAnimationFrame(rafId);
        rafId = null;
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 拖拽元素调整大小处理
  const handleElementResize = (elementId: string, e: React.MouseEvent, direction: string) => {
    const startX = e.clientX;
    const startY = e.clientY;
    const element = draggedElements.find(el => el.id === elementId);
    
    if (!element || !designAreaRef.current) return;
    
    const startWidth = element.width;
    const startHeight = element.height;
    const startX_pos = element.x;
    const startY_pos = element.y;
    const designRect = designAreaRef.current.getBoundingClientRect();
    
    // SVG的原始尺寸
    const svgWidth = 1350;
    const svgHeight = 420;
    const svgAspectRatio = svgWidth / svgHeight;
    
    // 设计区域的实际比例
    const areaAspectRatio = designRect.width / designRect.height;
    
    // 计算 SVG 在设计区域中的实际显示尺寸和缩放比例
    let scaleX, scaleY;
    
    if (areaAspectRatio > svgAspectRatio) {
      // 设计区域更宽，SVG会被高度限制
      const actualSvgHeight = designRect.height;
      const actualSvgWidth = actualSvgHeight * svgAspectRatio;
      scaleX = svgWidth / actualSvgWidth;
      scaleY = svgHeight / actualSvgHeight;
    } else {
      // 设计区域更高，SVG会被宽度限制
      const actualSvgWidth = designRect.width;
      const actualSvgHeight = actualSvgWidth / svgAspectRatio;
      scaleX = svgWidth / actualSvgWidth;
      scaleY = svgHeight / actualSvgHeight;
    }
    
    // 使用 requestAnimationFrame 优化性能和节流
    let rafId: number | null = null;
    let lastUpdateTime = 0;
    const THROTTLE_MS = 8; // 控制更新频率，约120fps
    
    const handleMouseMove = (e: MouseEvent) => {
      const now = Date.now();
      
      // 如果已经有待执行的动画帧，取消它
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
      
      // 节流处理，避免过于频繁的更新
      if (now - lastUpdateTime < THROTTLE_MS) {
        rafId = requestAnimationFrame(() => handleMouseMove(e));
        return;
      }
      
      rafId = requestAnimationFrame(() => {
        const deltaX = (e.clientX - startX) * scaleX;
        const deltaY = (e.clientY - startY) * scaleY;
        
        let newWidth = startWidth;
        let newHeight = startHeight;
        let newX = startX_pos;
        let newY = startY_pos;
        
        switch (direction) {
          case 'se': // 右下角
            newWidth = Math.max(50, startWidth + deltaX);
            newHeight = Math.max(50, startHeight + deltaY);
            break;
          case 'sw': // 左下角
            newWidth = Math.max(50, startWidth - deltaX);
            newHeight = Math.max(50, startHeight + deltaY);
            newX = startX_pos + (startWidth - newWidth);
            break;
          case 'ne': // 右上角
            newWidth = Math.max(50, startWidth + deltaX);
            newHeight = Math.max(50, startHeight - deltaY);
            newY = startY_pos + (startHeight - newHeight);
            break;
          case 'nw': // 左上角
            newWidth = Math.max(50, startWidth - deltaX);
            newHeight = Math.max(50, startHeight - deltaY);
            newX = startX_pos + (startWidth - newWidth);
            newY = startY_pos + (startHeight - newHeight);
            break;
          case 'n': // 上边缘 - 只调整高度
            newHeight = Math.max(50, startHeight - deltaY);
            newY = startY_pos + (startHeight - newHeight);
            break;
          case 's': // 下边缘 - 只调整高度
            newHeight = Math.max(50, startHeight + deltaY);
            break;
          case 'w': // 左边缘 - 只调整宽度
            newWidth = Math.max(50, startWidth - deltaX);
            newX = startX_pos + (startWidth - newWidth);
            break;
          case 'e': // 右边缘 - 只调整宽度
            newWidth = Math.max(50, startWidth + deltaX);
            break;
        }
        
        // 确保不超出边界（允许部分超出以实现更灵活的设计）
        newX = Math.max(-newWidth/2, Math.min(newX, 1350 - newWidth/2));
        newY = Math.max(-newHeight/2, Math.min(newY, 420 - newHeight/2));
        
        // 强制立即更新状态，避免延迟
        setDraggedElements(prev => {
          const newElements = prev.map(el => 
            el.id === elementId 
              ? { ...el, width: newWidth, height: newHeight, x: newX, y: newY }
              : el
          );
          
          // 添加调试信息
          const updatedElement = newElements.find(el => el.id === elementId);
          if (updatedElement) {
            console.log(`🔧 调整大小: ${direction}, 新尺寸: ${Math.round(updatedElement.width)}×${Math.round(updatedElement.height)}, 位置: (${Math.round(updatedElement.x)}, ${Math.round(updatedElement.y)})`);
          }
          
          return newElements;
        });
        
        lastUpdateTime = now;
        rafId = null;
      });
    };
    
    const handleMouseUp = () => {
      if (rafId) {
        cancelAnimationFrame(rafId);
        rafId = null;
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 获取键帽的精确位置和尺寸信息
  const getKeycapBounds = (keyId: string) => {
    const keycapElement = document.querySelector(`[data-key="${keyId}"]`) as SVGGElement;
    if (!keycapElement) return null;

    // 获取键帽的transform属性
    const transform = keycapElement.getAttribute('transform') || '';
    const translateMatch = transform.match(/translate\(([^,]+),([^)]+)\)/);

    if (!translateMatch) return null;

    let x = parseFloat(translateMatch[1]);
    let y = parseFloat(translateMatch[2]);

    // 处理缩放
    const scaleMatch = transform.match(/scale\(([^)]+)\)/);
    if (scaleMatch) {
      const scale = parseFloat(scaleMatch[1]);
      x *= scale;
      y *= scale;
    }

    // 获取键帽的实际尺寸
    const rectElements = keycapElement.querySelectorAll('rect');
    let width = 41;  // 默认宽度
    let height = 36; // 默认高度

    if (rectElements.length > 0) {
      // 获取底部基座的尺寸（第一个rect元素）
      const baseRect = rectElements[0];
      width = parseFloat(baseRect.getAttribute('width') || '41');
      height = parseFloat(baseRect.getAttribute('height') || '36');
    }

    return { x, y, width, height };
  };

  // 适配键盘 - 只覆盖选中的键帽
  const fitToKeyboard = (elementId: string) => {
    if (!designAreaRef.current) return;
    
    const element = draggedElements.find(el => el.id === elementId);
    if (!element) return;
    
    // 如果没有选中的键帽，提示用户先选择键帽
    if (selectedKeys.length === 0) {
      alert('请先选择要应用图片的键帽！\n点击键帽进行选择，然后再使用"适配键盘"功能。');
      return;
    }
    
    // 移除原来的图片元素
    setDraggedElements(prev => prev.filter(el => el.id !== elementId));
    
    // 为每个选中的键帽创建一个单独的图片元素
    const newElements: DraggedElement[] = [];
    
    selectedKeys.forEach((keyId, index) => {
      const bounds = getKeycapBounds(keyId);
      if (bounds) {
        // 创建新的图片元素 - 精确对齐键帽位置
        const newElement: DraggedElement = {
          id: `${elementId}-keycap-${index}-${Date.now()}`,
          x: bounds.x, // 直接使用键帽的SVG坐标
          y: bounds.y + 30, // 考虑键盘整体的30px偏移
          width: bounds.width,
          height: bounds.height,
          imageUrl: element.imageUrl,
          opacity: element.opacity,
          borderRadius: 4, // 匹配键帽圆角
          rotation: element.rotation
        };

        newElements.push(newElement);

        // 调试信息
        console.log(`🔍 键帽 ${keyId} 位置信息:`, {
          SVG坐标: { x: bounds.x, y: bounds.y },
          最终坐标: { x: newElement.x, y: newElement.y },
          尺寸: { width: bounds.width, height: bounds.height }
        });
      } else {
        console.warn(`⚠️ 无法获取键帽 ${keyId} 的位置信息`);
      }
    });
    
    // 添加所有新创建的键帽图片元素
    setDraggedElements(prev => [...prev, ...newElements]);
    
    // 选中第一个新创建的元素
    if (newElements.length > 0) {
      setSelectedElement(newElements[0].id);
    }
    
    // 提示用户操作完成
    alert(`已为 ${selectedKeys.length} 个选中的键帽创建图片覆盖层！\n图片现在只覆盖在键帽上，键帽间的空隙保持透明。`);
  };

  // 平铺整个键盘 - 与适配键盘相同功能
  const tileToKeyboard = (elementId: string) => {
    fitToKeyboard(elementId);
  };

  // 更新元素属性
  const updateElementProperty = (elementId: string, property: keyof DraggedElement, value: any) => {
    setDraggedElements(prev => prev.map(el => 
      el.id === elementId 
        ? { ...el, [property]: value }
        : el
    ));
  };

  // 键帽个性化设置管理函数
  const getKeycapCustomization = (keyId: string): KeycapCustomization => {
    const customization = keycapCustomizations[keyId] || {
      keyId,
      textColor: '#000000',
      fontSize: 14,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'bold',
      textStyle: 'normal',
      textDecoration: 'none',
      backgroundOpacity: 1,
      backgroundScale: 1,
      backgroundPosition: { x: 0, y: 0 },
      coverageMode: 'full-keycap' // 默认覆盖整个键帽
    };

    // 添加调试信息 - 只在有背景图片时输出
    if (customization.backgroundImage) {
      console.log(`🔍 键帽 ${keyId} 的自定义设置:`, customization);
    }

    return customization;
  };

  const updateKeycapCustomization = (keyId: string, updates: Partial<KeycapCustomization>) => {
    setKeycapCustomizations(prev => ({
      ...prev,
      [keyId]: {
        ...getKeycapCustomization(keyId),
        ...updates
      }
    }));
  };

  const applyImageToKeycap = (keyId: string, imageUrl: string) => {
    updateKeycapCustomization(keyId, {
      backgroundImage: imageUrl,
      backgroundOpacity: 0.8,
      backgroundScale: 1,
      backgroundPosition: { x: 0, y: 0 },
      coverageMode: 'full-keycap' // 默认覆盖整个键帽
    });
  };

  const removeKeycapImage = (keyId: string) => {
    updateKeycapCustomization(keyId, {
      backgroundImage: undefined
    });
  };

  // 处理素材分类选择
  const handleCategorySelect = (categoryId: string) => {
    console.log(`🔍 点击分类: ${categoryId}`);
    console.log(`📦 当前公共素材总数: ${publicMaterials.length}`);
    console.log(`🔍 公共素材详情:`, publicMaterials.map(m => ({ name: m.name, category: m.category, url: m.url })));
    
    setSelectedCategory(categoryId);
    setMaterialLibraryView('materials');
    
    // 根据分类过滤素材
    let filtered;
    if (categoryId === 'all') {
      // 显示所有素材
      filtered = publicMaterials;
    } else {
      // 根据分类过滤
      filtered = publicMaterials.filter(material => {
        const matchCategory = material.category === categoryId;
        const matchTags = material.tags && material.tags.includes(categoryId);
        return matchCategory || matchTags;
      });
    }
    setFilteredMaterials(filtered);
    console.log(`✅ 选择分类: ${categoryId}, 找到素材: ${filtered.length}个`);
    console.log(`🔍 过滤后的素材:`, filtered.map(m => ({ name: m.name, category: m.category })));
  };

  // 返回分类选择页面
  const backToCategories = () => {
    setMaterialLibraryView('categories');
    setSelectedCategory(null);
    setFilteredMaterials([]);
  };

  // 点击素材应用到键盘
  const applyMaterialToKeyboard = (material: UploadedImage) => {
    // 创建一个新的可拖拽元素，覆盖整个键盘区域
    const newElement: DraggedElement = {
      id: `material-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      x: 0,
      y: 0,
      width: 1350, // 键盘区域宽度
      height: 420, // 键盘区域高度
      imageUrl: material.url,
      opacity: 0.7, // 默认70%透明度
      borderRadius: 0,
      rotation: 0
    };
    
    // 添加到拖拽元素列表
    setDraggedElements(prev => [...prev, newElement]);
    
    // 设置为选中状态，方便用户立即编辑
    setSelectedElement(newElement.id);
    setEditingMode('image');
    
    console.log('✅ 素材已应用为可拖拽图层:', material.name);
    console.log('🔍 创建的图层信息:', newElement);
  };

  // 应用素材到键盘并超出键帽间距9.1mm
  const applyMaterialToKeyboardWithExtension = (material: UploadedImage) => {
    // 键帽间距计算：
    // - 键帽尺寸：55px = 18.2mm
    // - 键帽间距：2px = 0.8mm (57px - 55px)
    // - mm到px转换比例：55/18.2 ≈ 3.02px/mm
    const mmToPx = 55 / 18.2; // 1mm = 3.02px
    const keycapGapMm = 0.8; // 键帽间距0.8mm
    const extensionMm = 9.1; // 要扩展的距离9.1mm

    // 计算扩展像素：9.1mm相对于0.8mm键帽间距的比例
    const extensionPx = Math.round(extensionMm * mmToPx); // 9.1mm ≈ 28px
    const keycapGapPx = Math.round(keycapGapMm * mmToPx); // 0.8mm ≈ 2px

    // 计算键盘的实际有效区域（精确的键帽分布范围）
    // 108键布局：从ESC键(20,15)开始，到小键盘最右下角结束
    const keyboardStartX = 20;   // ESC键左边界
    const keyboardStartY = 15;   // ESC键上边界
    const keyboardEndX = 1307;   // 小键盘最右边键帽右边界 (1252+55)
    const keyboardEndY = 383;    // 主键盘底部控制键下边界 (328+55)

    // 创建一个新的可拖拽元素，覆盖键盘有效区域并向外扩展9.1mm
    const newElement: DraggedElement = {
      id: `material-extended-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      x: keyboardStartX - extensionPx, // 从键盘左边界向左扩展9.1mm
      y: keyboardStartY - extensionPx, // 从键盘上边界向上扩展9.1mm
      width: (keyboardEndX - keyboardStartX) + extensionPx * 2, // 键盘宽度 + 左右各扩展9.1mm
      height: (keyboardEndY - keyboardStartY) + extensionPx * 2, // 键盘高度 + 上下各扩展9.1mm
      imageUrl: material.url,
      opacity: 0.7, // 默认70%透明度
      borderRadius: 0,
      rotation: 0
    };

    // 添加到拖拽元素列表
    setDraggedElements(prev => [...prev, newElement]);

    // 设置为选中状态，方便用户立即编辑
    setSelectedElement(newElement.id);
    setEditingMode('image');

    console.log('✅ 素材已应用为扩展图层（相对键帽间距扩展9.1mm）:', material.name);
    console.log('🔍 键盘有效区域:', `(${keyboardStartX},${keyboardStartY}) → (${keyboardEndX},${keyboardEndY})`);
    console.log('🔍 键帽间距:', `${keycapGapPx}px (${keycapGapMm}mm)`);
    console.log('🔍 扩展距离:', `${extensionPx}px (${extensionMm}mm)`);
    console.log('🔍 扩展比例:', `${(extensionMm / keycapGapMm).toFixed(1)}倍键帽间距`);
    console.log('🔍 最终尺寸:', `${newElement.width}×${newElement.height}px, 位置:(${newElement.x}, ${newElement.y})`);
  };

  // 应用素材到所有键帽背景
  const applyMaterialToKeycaps = (material: UploadedImage) => {
    // 获取当前键盘布局的所有键帽ID
    const getAllKeycapIds = () => {
      const keycapIds: string[] = [];
      
      // 根据不同布局获取所有键帽ID - 使用实际的键帽ID格式
      if (designSettings.layout === '108') {
        // 108键布局的所有键帽ID - 使用实际渲染中的ID
        const keyIds = [
          // 功能键行
          'key-esc', 'key-f1', 'key-f2', 'key-f3', 'key-f4', 'key-f5', 'key-f6', 'key-f7', 'key-f8', 'key-f9', 'key-f10', 'key-f11', 'key-f12', 'key-prt', 'key-scr', 'key-pau',
          // 数字行 - 注意：波浪号键使用 key-grave 而不是 key-tilde
          'key-grave', 'key-1', 'key-2', 'key-3', 'key-4', 'key-5', 'key-6', 'key-7', 'key-8', 'key-9', 'key-0', 'key-minus', 'key-equal', 'key-backspace', 'key-ins', 'key-home', 'key-pageup', 'key-numlock', 'key-numdiv', 'key-nummul', 'key-numsub',
          // QWERTY行
          'key-tab', 'key-q', 'key-w', 'key-e', 'key-r', 'key-t', 'key-y', 'key-u', 'key-i', 'key-o', 'key-p', 'key-lbracket', 'key-rbracket', 'key-backslash', 'key-del', 'key-end', 'key-pagedown', 'key-num7', 'key-num8', 'key-num9', 'key-numadd',
          // ASDF行
          'key-caps', 'key-a', 'key-s', 'key-d', 'key-f', 'key-g', 'key-h', 'key-j', 'key-k', 'key-l', 'key-semicolon', 'key-quote', 'key-enter', 'key-num4', 'key-num5', 'key-num6',
          // ZXCV行
          'key-lshift', 'key-z', 'key-x', 'key-c', 'key-v', 'key-b', 'key-n', 'key-m', 'key-comma', 'key-period', 'key-slash', 'key-rshift', 'key-up', 'key-num1', 'key-num2', 'key-num3', 'key-numenter',
          // 底部行
          'key-lctrl', 'key-lwin', 'key-lalt', 'key-space', 'key-ralt', 'key-fn', 'key-menu', 'key-rctrl', 'key-left', 'key-down', 'key-right', 'key-num0', 'key-numdot'
        ];
        keycapIds.push(...keyIds);
      } else if (designSettings.layout === '87') {
        // 87键布局的所有键帽ID
        const keyIds = [
          // 功能键行
          'key-87-esc', 'key-87-f1', 'key-87-f2', 'key-87-f3', 'key-87-f4', 'key-87-f5', 'key-87-f6', 'key-87-f7', 'key-87-f8', 'key-87-f9', 'key-87-f10', 'key-87-f11', 'key-87-f12', 'key-87-prt', 'key-87-scr', 'key-87-pau',
          // 数字行
          'key-87-tilde', 'key-87-1', 'key-87-2', 'key-87-3', 'key-87-4', 'key-87-5', 'key-87-6', 'key-87-7', 'key-87-8', 'key-87-9', 'key-87-0', 'key-87--', 'key-87-=', 'key-87-backspace', 'key-87-ins', 'key-87-home', 'key-87-pageup',
          // QWERTY行
          'key-87-tab', 'key-87-q', 'key-87-w', 'key-87-e', 'key-87-r', 'key-87-t', 'key-87-y', 'key-87-u', 'key-87-i', 'key-87-o', 'key-87-p', 'key-87-[', 'key-87-]', 'key-87-backslash', 'key-87-del', 'key-87-end', 'key-87-pagedown',
          // ASDF行
          'key-87-caps', 'key-87-a', 'key-87-s', 'key-87-d', 'key-87-f', 'key-87-g', 'key-87-h', 'key-87-j', 'key-87-k', 'key-87-l', 'key-87-semicolon', 'key-87-quote', 'key-87-enter',
          // ZXCV行
          'key-87-lshift', 'key-87-z', 'key-87-x', 'key-87-c', 'key-87-v', 'key-87-b', 'key-87-n', 'key-87-m', 'key-87-comma', 'key-87-period', 'key-87-slash', 'key-87-rshift', 'key-87-up',
          // 底部行
          'key-87-lctrl', 'key-87-win', 'key-87-lalt', 'key-87-space', 'key-87-ralt', 'key-87-fn', 'key-87-menu', 'key-87-rctrl', 'key-87-left', 'key-87-down', 'key-87-right'
        ];
        keycapIds.push(...keyIds);
      } else if (designSettings.layout === '87-side') {
        // 87键侧刻布局的所有键帽ID
        const keyIds = [
          // 功能键行
          'key-87side-esc', 'key-87side-f1', 'key-87side-f2', 'key-87side-f3', 'key-87side-f4', 'key-87side-f5', 'key-87side-f6', 'key-87side-f7', 'key-87side-f8', 'key-87side-f9', 'key-87side-f10', 'key-87side-f11', 'key-87side-f12', 'key-87side-prt', 'key-87side-scr', 'key-87side-pau',
          // 数字行
          'key-87side-tilde', 'key-87side-1', 'key-87side-2', 'key-87side-3', 'key-87side-4', 'key-87side-5', 'key-87side-6', 'key-87side-7', 'key-87side-8', 'key-87side-9', 'key-87side-0', 'key-87side--', 'key-87side-=', 'key-87side-backspace', 'key-87side-ins', 'key-87side-home', 'key-87side-pageup',
          // QWERTY行
          'key-87side-tab', 'key-87side-q', 'key-87side-w', 'key-87side-e', 'key-87side-r', 'key-87side-t', 'key-87side-y', 'key-87side-u', 'key-87side-i', 'key-87side-o', 'key-87side-p', 'key-87side-[', 'key-87side-]', 'key-87side-backslash', 'key-87side-del', 'key-87side-end', 'key-87side-pagedown',
          // ASDF行
          'key-87side-caps', 'key-87side-a', 'key-87side-s', 'key-87side-d', 'key-87side-f', 'key-87side-g', 'key-87side-h', 'key-87side-j', 'key-87side-k', 'key-87side-l', 'key-87side-semicolon', 'key-87side-quote', 'key-87side-enter',
          // ZXCV行
          'key-87side-lshift', 'key-87side-z', 'key-87side-x', 'key-87side-c', 'key-87side-v', 'key-87side-b', 'key-87side-n', 'key-87side-m', 'key-87side-comma', 'key-87side-period', 'key-87side-slash', 'key-87side-rshift', 'key-87side-up',
          // 底部行
          'key-87side-lctrl', 'key-87side-win', 'key-87side-lalt', 'key-87side-space', 'key-87side-ralt', 'key-87side-fn', 'key-87side-menu', 'key-87side-rctrl', 'key-87side-left', 'key-87side-down', 'key-87side-right'
        ];
        keycapIds.push(...keyIds);
      }
      
      return keycapIds;
    };
    
    // 获取所有键帽ID
    const allKeycapIds = getAllKeycapIds();
    
    // 为每个键帽设置背景图片
    const newCustomizations: Record<string, KeycapCustomization> = {};
    
    allKeycapIds.forEach(keyId => {
      const existingCustomization = getKeycapCustomization(keyId);
      newCustomizations[keyId] = {
        ...existingCustomization,
        backgroundImage: material.url,
        backgroundOpacity: 0.7, // 设置为70%透明度，让用户能看到键帽轮廓
        backgroundScale: 1.0, // 默认缩放
        backgroundPosition: { x: 0, y: 0 }, // 默认位置
        coverageMode: 'full-keycap' // 覆盖整个键帽
      };
    });
    
    // 批量更新键帽自定义设置
    setKeycapCustomizations(prev => ({
      ...prev,
      ...newCustomizations
    }));
    
    console.log('✅ 素材已应用到所有键帽背景:', material.name, `共${allKeycapIds.length}个键帽`);
    console.log('🔍 应用的键帽ID列表:', allKeycapIds);
    console.log('🔍 素材URL:', material.url);
  };

  // 清除所有键帽背景
  const clearAllKeycapBackgrounds = () => {
    const confirmed = window.confirm('确定要清除所有键帽的背景图片吗？');
    if (!confirmed) return;
    
    // 清除所有键帽的背景图片
    const clearedCustomizations: Record<string, KeycapCustomization> = {};
    
    Object.keys(keycapCustomizations).forEach(keyId => {
      const existingCustomization = keycapCustomizations[keyId];
      clearedCustomizations[keyId] = {
        ...existingCustomization,
        backgroundImage: undefined, // 清除背景图片
        backgroundOpacity: 0.7,
        backgroundScale: 1.0,
        backgroundPosition: { x: 0, y: 0 }
      };
    });
    
    setKeycapCustomizations(clearedCustomizations);
    console.log('✅ 已清除所有键帽背景');
  };

  // 将图片URL转换为base64的辅助函数
  const imageToBase64 = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      // 如果已经是base64格式，直接返回
      if (url.startsWith('data:')) {
        resolve(url);
        return;
      }

      const img = new Image();

      // 处理跨域问题
      if (url.startsWith('http') && !url.includes(window.location.hostname)) {
        img.crossOrigin = 'anonymous';
      }

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('无法创建canvas上下文'));
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        try {
          // 尝试获取原始格式，如果失败则使用PNG
          let dataURL: string;
          if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            dataURL = canvas.toDataURL('image/jpeg', 0.9);
          } else if (url.toLowerCase().includes('.webp')) {
            dataURL = canvas.toDataURL('image/webp', 0.9);
          } else {
            dataURL = canvas.toDataURL('image/png');
          }
          resolve(dataURL);
        } catch (error) {
          // 如果转换失败，尝试使用PNG格式
          try {
            const pngDataURL = canvas.toDataURL('image/png');
            resolve(pngDataURL);
          } catch (pngError) {
            reject(new Error(`图片转换失败: ${error}`));
          }
        }
      };

      img.onerror = (error) => {
        console.warn('图片加载失败，保留原始URL:', url, error);
        // 如果图片加载失败，返回原始URL作为备用
        resolve(url);
      };

      img.src = url;
    });
  };

  // 导出键帽单个排列的SVG函数
  const exportKeycapsAsSeparateSVG = async () => {
    try {
      // 定义键帽的标准排列顺序（按照您提供的图片顺序）
      const keycapOrder = [
        // 第一行：功能键区
        ['ESC', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'PRT', 'SCR', 'PAU', 'INS'],
        // 第二行：数字键区
        ['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'BACK', 'HOME', 'PGUP', '/', '*'],
        // 第三行：QWERTY行
        ['TAB', 'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\', 'DEL', 'END', 'PGDN'],
        // 第四行：ASDF行
        ['CAPS', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'", 'ENTER', '7', '8', '9', '+'],
        // 第五行：ZXCV行
        ['SHIFT', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/', 'SHIFT', '↑', '4', '5', '6'],
        // 第六行：控制键行
        ['CTRL', 'WIN', 'ALT', 'SPACE', 'ALT', 'FN', 'MENU', 'CTRL', '←', '↓', '→', '1', '2', '3', 'ENT'],
        // 第七行：小键盘底部
        ['0', '.']
      ];

      // 计算SVG尺寸
      const keycapSize = 60; // 键帽大小
      const spacing = 10; // 键帽间距
      const margin = 20; // 边距
      const maxKeysPerRow = Math.max(...keycapOrder.map(row => row.length));

      const svgWidth = margin * 2 + maxKeysPerRow * keycapSize + (maxKeysPerRow - 1) * spacing;
      const svgHeight = margin * 2 + keycapOrder.length * keycapSize + (keycapOrder.length - 1) * spacing;

      // 创建新的SVG元素
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      svg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
      svg.setAttribute('width', svgWidth.toString());
      svg.setAttribute('height', svgHeight.toString());
      svg.setAttribute('viewBox', `0 0 ${svgWidth} ${svgHeight}`);

      // 添加背景
      const background = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      background.setAttribute('width', '100%');
      background.setAttribute('height', '100%');
      background.setAttribute('fill', '#f8f9fa');
      svg.appendChild(background);

      // 收集所有键帽的自定义设置
      const allKeycapCustomizations = { ...keycapCustomizations };

      // 渲染键帽
      let keycapIndex = 0;
      keycapOrder.forEach((row, rowIndex) => {
        row.forEach((keyLabel, colIndex) => {
          const x = margin + colIndex * (keycapSize + spacing);
          const y = margin + rowIndex * (keycapSize + spacing);

          // 创建键帽组
          const keycapGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
          keycapGroup.setAttribute('transform', `translate(${x}, ${y})`);

          // 键帽背景
          const keycapBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          keycapBg.setAttribute('width', keycapSize.toString());
          keycapBg.setAttribute('height', keycapSize.toString());
          keycapBg.setAttribute('rx', '8');
          keycapBg.setAttribute('ry', '8');
          keycapBg.setAttribute('fill', '#ffffff');
          keycapBg.setAttribute('stroke', '#d1d5db');
          keycapBg.setAttribute('stroke-width', '1');
          keycapGroup.appendChild(keycapBg);

          // 查找对应的键帽自定义设置
          const keyId = findKeyIdByLabel(keyLabel);
          const customization = keyId ? allKeycapCustomizations[keyId] : null;

          // 如果有背景图片，添加图片
          if (customization?.backgroundImage) {
            const image = document.createElementNS('http://www.w3.org/2000/svg', 'image');
            image.setAttribute('x', '5');
            image.setAttribute('y', '5');
            image.setAttribute('width', (keycapSize - 10).toString());
            image.setAttribute('height', (keycapSize - 10).toString());
            image.setAttribute('href', customization.backgroundImage);
            image.setAttribute('preserveAspectRatio', 'xMidYMid slice');
            if (customization.backgroundOpacity !== undefined) {
              image.setAttribute('opacity', customization.backgroundOpacity.toString());
            }
            keycapGroup.appendChild(image);
          }

          // 键帽标签
          const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          text.setAttribute('x', (keycapSize / 2).toString());
          text.setAttribute('y', (keycapSize / 2 + 5).toString());
          text.setAttribute('text-anchor', 'middle');
          text.setAttribute('font-family', 'Arial, sans-serif');
          text.setAttribute('font-size', keyLabel.length > 4 ? '10' : '12');
          text.setAttribute('font-weight', '600');
          text.setAttribute('fill', '#374151');
          text.textContent = keyLabel;
          keycapGroup.appendChild(text);

          svg.appendChild(keycapGroup);
          keycapIndex++;
        });
      });

      // 转换图片为base64
      const imageUrls = new Set<string>();
      Object.values(allKeycapCustomizations).forEach(customization => {
        if (customization.backgroundImage) {
          imageUrls.add(customization.backgroundImage);
        }
      });

      console.log('🔄 开始转换键帽图片为base64格式，共', imageUrls.size, '张图片');

      const imageMap = new Map<string, string>();
      const conversionPromises = Array.from(imageUrls).map(async (url) => {
        try {
          const base64 = await imageToBase64(url);
          imageMap.set(url, base64);
          console.log('✅ 键帽图片转换成功:', url.substring(url.lastIndexOf('/') + 1));
        } catch (error) {
          console.warn('⚠️ 键帽图片转换失败:', url, error);
          imageMap.set(url, url);
        }
      });

      await Promise.all(conversionPromises);

      // 替换SVG中的图片URL为base64
      const imageElements = svg.querySelectorAll('image');
      imageElements.forEach(img => {
        const href = img.getAttribute('href');
        if (href && imageMap.has(href)) {
          const base64 = imageMap.get(href);
          if (base64) {
            img.setAttribute('href', base64);
          }
        }
      });

      // 添加设计信息注释
      const comment = document.createComment(`
        键帽单个排列设计稿
        布局: ${KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name || '未知'}
        自定义键帽数量: ${Object.keys(allKeycapCustomizations).filter(k => allKeycapCustomizations[k].backgroundImage).length}
        嵌入图片数量: ${imageUrls.size}
        导出时间: ${new Date().toLocaleString('zh-CN')}
      `);
      svg.insertBefore(comment, svg.firstChild);

      // 创建完整的SVG字符串
      const svgString = new XMLSerializer().serializeToString(svg);

      // 创建Blob对象
      const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const layoutName = KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name || '键盘';
      const fileName = designName.trim() || `${layoutName}键帽排列_${timestamp}`;
      link.download = `${fileName}_键帽排列.svg`;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);

      console.log('✅ 键帽排列SVG导出成功:', fileName);
      console.log('📊 导出统计:', {
        布局: layoutName,
        自定义键帽: Object.keys(allKeycapCustomizations).filter(k => allKeycapCustomizations[k].backgroundImage).length,
        嵌入图片: imageUrls.size,
        文件大小: `${(blob.size / 1024).toFixed(2)} KB`
      });
      alert(`键帽排列设计稿已导出为 "${fileName}_键帽排列.svg"\n\n包含内容:\n- 键盘布局: ${layoutName}\n- 自定义键帽: ${Object.keys(allKeycapCustomizations).filter(k => allKeycapCustomizations[k].backgroundImage).length}个\n- 嵌入图片: ${imageUrls.size}张\n- 排列方式: 单个键帽按行排列`);

    } catch (error) {
      console.error('❌ 导出键帽排列SVG失败:', error);
      alert(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 查找键帽ID的辅助函数
  const findKeyIdByLabel = (label: string): string | null => {
    // 这里需要根据实际的键帽ID映射来查找
    const labelToIdMap: { [key: string]: string } = {
      'ESC': 'key-esc',
      'F1': 'key-f1', 'F2': 'key-f2', 'F3': 'key-f3', 'F4': 'key-f4',
      'F5': 'key-f5', 'F6': 'key-f6', 'F7': 'key-f7', 'F8': 'key-f8',
      'F9': 'key-f9', 'F10': 'key-f10', 'F11': 'key-f11', 'F12': 'key-f12',
      'PRT': 'key-prt', 'SCR': 'key-scr', 'PAU': 'key-pau',
      '~': 'key-tilde', '1': 'key-1', '2': 'key-2', '3': 'key-3', '4': 'key-4',
      '5': 'key-5', '6': 'key-6', '7': 'key-7', '8': 'key-8', '9': 'key-9', '0': 'key-0',
      '-': 'key-minus', '=': 'key-equal', 'BACK': 'key-backspace',
      'TAB': 'key-tab', 'Q': 'key-q', 'W': 'key-w', 'E': 'key-e', 'R': 'key-r',
      'T': 'key-t', 'Y': 'key-y', 'U': 'key-u', 'I': 'key-i', 'O': 'key-o', 'P': 'key-p',
      '[': 'key-leftbracket', ']': 'key-rightbracket', '\\': 'key-backslash',
      'CAPS': 'key-caps', 'A': 'key-a', 'S': 'key-s', 'D': 'key-d', 'F': 'key-f',
      'G': 'key-g', 'H': 'key-h', 'J': 'key-j', 'K': 'key-k', 'L': 'key-l',
      ';': 'key-semicolon', "'": 'key-quote', 'ENTER': 'key-enter',
      'SHIFT': 'key-lshift', 'Z': 'key-z', 'X': 'key-x', 'C': 'key-c', 'V': 'key-v',
      'B': 'key-b', 'N': 'key-n', 'M': 'key-m', ',': 'key-comma', '.': 'key-period',
      '/': 'key-slash', '↑': 'key-up',
      'CTRL': 'key-lctrl', 'WIN': 'key-lwin', 'ALT': 'key-lalt', 'SPACE': 'key-space',
      'FN': 'key-fn', 'MENU': 'key-menu', '←': 'key-left', '↓': 'key-down', '→': 'key-right',
      // 小键盘和其他键
      'INS': 'key-ins', 'HOME': 'key-home', 'PGUP': 'key-pageup',
      'DEL': 'key-del', 'END': 'key-end', 'PGDN': 'key-pagedown',
      'NUM*': 'key-nummul', 'NUM+': 'key-numadd', 'ENT': 'key-numenter', 'NUM.': 'key-numdot'
    };

    return labelToIdMap[label] || null;
  };

  // 原有的导出SVG设计稿的函数
  const exportDesignAsSVG = async () => {
    try {
      // 获取当前SVG元素
      const svgElement = document.querySelector('svg[viewBox="0 0 1350 420"]');
      if (!svgElement) {
        alert('无法找到设计画布，请稍后重试');
        return;
      }

      // 克隆SVG元素以避免修改原始元素
      const clonedSVG = svgElement.cloneNode(true) as SVGElement;

      // 设置SVG的基本属性
      clonedSVG.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      clonedSVG.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
      clonedSVG.setAttribute('width', '1350');
      clonedSVG.setAttribute('height', '420');

      // 收集所有需要转换的图片URL
      const imageUrls = new Set<string>();

      // 收集拖拽元素中的图片
      draggedElements.forEach(element => {
        if (element.imageUrl) {
          imageUrls.add(element.imageUrl);
        }
      });

      // 收集键帽自定义背景图片
      Object.values(keycapCustomizations).forEach(customization => {
        if (customization.backgroundImage) {
          imageUrls.add(customization.backgroundImage);
        }
      });

      // 查找SVG中的所有image元素
      const imageElements = clonedSVG.querySelectorAll('image');
      imageElements.forEach(img => {
        const href = img.getAttribute('href') || img.getAttribute('xlink:href');
        if (href && href.startsWith('http')) {
          imageUrls.add(href);
        }
      });

      console.log('🔄 开始转换图片为base64格式，共', imageUrls.size, '张图片');

      // 将所有图片转换为base64
      const imageMap = new Map<string, string>();
      const conversionPromises = Array.from(imageUrls).map(async (url) => {
        try {
          const base64 = await imageToBase64(url);
          imageMap.set(url, base64);
          console.log('✅ 图片转换成功:', url.substring(url.lastIndexOf('/') + 1));
        } catch (error) {
          console.warn('⚠️ 图片转换失败:', url, error);
          // 保留原始URL作为备用
          imageMap.set(url, url);
        }
      });

      await Promise.all(conversionPromises);

      // 替换SVG中的图片URL为base64
      const replaceImageUrls = (element: Element) => {
        if (element.tagName === 'image') {
          const href = element.getAttribute('href') || element.getAttribute('xlink:href');
          if (href && imageMap.has(href)) {
            const base64 = imageMap.get(href);
            if (base64) {
              element.setAttribute('href', base64);
              element.removeAttribute('xlink:href');
            }
          }
        }

        // 递归处理子元素
        Array.from(element.children).forEach(child => {
          replaceImageUrls(child);
        });
      };

      replaceImageUrls(clonedSVG);

      // 移除所有交互相关的属性和事件监听器
      const removeInteractiveElements = (element: Element) => {
        // 移除事件相关属性
        element.removeAttribute('onclick');
        element.removeAttribute('onmousedown');
        element.removeAttribute('onmouseover');
        element.removeAttribute('onmouseout');
        element.removeAttribute('ondragover');
        element.removeAttribute('ondrop');
        element.removeAttribute('data-key');

        // 移除CSS类名中的交互相关类
        const className = element.getAttribute('class');
        if (className) {
          const cleanedClassName = className
            .replace(/cursor-\w+/g, '')
            .replace(/hover:\w+/g, '')
            .replace(/transition-\w+/g, '')
            .replace(/keycap-group/g, '')
            .trim();
          if (cleanedClassName) {
            element.setAttribute('class', cleanedClassName);
          } else {
            element.removeAttribute('class');
          }
        }

        // 移除style中的pointer-events和其他交互相关样式
        const style = element.getAttribute('style');
        if (style) {
          const cleanedStyle = style
            .replace(/pointer-events:\s*[^;]+;?/g, '')
            .replace(/cursor:\s*[^;]+;?/g, '')
            .trim();
          if (cleanedStyle) {
            element.setAttribute('style', cleanedStyle);
          } else {
            element.removeAttribute('style');
          }
        }

        // 递归处理子元素
        Array.from(element.children).forEach(child => {
          removeInteractiveElements(child);
        });
      };

      removeInteractiveElements(clonedSVG);

      // 添加设计信息注释
      const comment = document.createComment(`
        键帽设计稿
        布局: ${KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name || '未知'}
        设计元素数量: ${draggedElements.length}
        自定义键帽数量: ${Object.keys(keycapCustomizations).filter(k => keycapCustomizations[k].backgroundImage).length}
        嵌入图片数量: ${imageUrls.size}
        导出时间: ${new Date().toLocaleString('zh-CN')}
      `);
      clonedSVG.insertBefore(comment, clonedSVG.firstChild);

      // 创建完整的SVG字符串
      const svgString = new XMLSerializer().serializeToString(clonedSVG);

      // 创建Blob对象
      const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const layoutName = KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name || '键盘';
      const fileName = designName.trim() || `${layoutName}设计_${timestamp}`;
      link.download = `${fileName}.svg`;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);

      console.log('✅ SVG设计稿导出成功:', fileName);
      console.log('📊 导出统计:', {
        布局: layoutName,
        设计元素: draggedElements.length,
        自定义键帽: Object.keys(keycapCustomizations).filter(k => keycapCustomizations[k].backgroundImage).length,
        嵌入图片: imageUrls.size,
        文件大小: `${(blob.size / 1024).toFixed(2)} KB`
      });
      alert(`设计稿已导出为 "${fileName}.svg"\n\n包含内容:\n- 键盘布局: ${layoutName}\n- 设计元素: ${draggedElements.length}个\n- 自定义键帽: ${Object.keys(keycapCustomizations).filter(k => keycapCustomizations[k].backgroundImage).length}个\n- 嵌入图片: ${imageUrls.size}张`);

    } catch (error) {
      console.error('❌ 导出SVG失败:', error);
      alert(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const saveDesign = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      alert('请先登录后再保存设计');
      return;
    }

    if (!designName.trim()) {
      alert('请输入设计名称');
      return;
    }

    if (draggedElements.length === 0) {
      alert('请先添加一些设计元素');
      return;
    }

    setIsSaving(true);

    try {
      // 构造设计数据
      const designData = {
        designName: designName.trim(),
        description: `基于${KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name}布局的键帽设计`,
        layoutId: parseInt(designSettings.layout) || 108, // 默认为108
        materialId: null, // 暂时为空，后续可以添加材质选择
        profileId: null,  // 暂时为空，后续可以添加高度选择
        designData: JSON.stringify({
          settings: designSettings,
          selectedKeys,
          elements: draggedElements.map(element => {
            // 为每个元素添加更多识别信息
            const sourceImage = uploadedImages.find(img => img.url === element.imageUrl);
            return {
              ...element,
              // 添加图片识别信息
              imageName: sourceImage?.name || `image_${element.id}`,
              materialId: sourceImage?.materialId || null,
              imageWidth: sourceImage?.width || null,
              imageHeight: sourceImage?.height || null,
              originalUrl: element.imageUrl
            };
          }),
          keycapCustomizations, // 添加键帽自定义设置
          timestamp: new Date().toISOString(),
          version: '1.0'
        }),
        previewImage: null, // 暂时为空，后续可以添加截图功能
        isPublished: false,
        isSelling: false,
        sellingPrice: null
      };

      console.log('保存设计数据:', designData);

      let response;

      // 判断是更新还是创建新设计
      if (currentDesignId) {
        // 更新现有设计
        console.log('🔄 更新现有设计，设计ID：', currentDesignId);
        response = await fetch(`http://localhost:8080/api/designs/${currentDesignId}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(designData)
        });
      } else {
        // 创建新设计
        console.log('✨ 创建新设计');
        response = await fetch('http://localhost:8080/api/designs', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(designData)
        });
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`保存失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      if (result.code === 200) {
        const actionText = currentDesignId ? '更新' : '保存';
        alert(`设计 "${designName}" ${actionText}成功！\n设计ID: ${result.data.designId}`);
        console.log(`设计${actionText}成功:`, result.data);

        // 如果是新创建的设计，设置currentDesignId以便后续更新
        if (!currentDesignId) {
          setCurrentDesignId(result.data.designId);
          // 更新URL参数，但不刷新页面
          const newUrl = `${window.location.pathname}?id=${result.data.designId}`;
          window.history.replaceState({}, '', newUrl);
        }
      } else {
        throw new Error(result.message || '保存失败');
      }

    } catch (error) {
      console.error('保存设计失败:', error);
      alert(`保存设计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsSaving(false);
    }
  };

  // 恢复键帽点击选择功能
  const handleKeyClick = (keyId: string) => {
    console.log('🔧 键帽点击:', keyId); // 调试信息
    
    // 设置当前编辑的键帽和编辑模式
    if (selectedKeycap === keyId) {
      // 如果点击的是当前编辑的键帽，则取消编辑
      setSelectedKeycap(null);
      setEditingMode(null);
      console.log('❌ 取消编辑键帽:', keyId);
    } else {
      // 选择新的键帽进行编辑
      setSelectedKeycap(keyId);
      setEditingMode('keycap');
      console.log('✅ 选择编辑键帽:', keyId);
    }
    
    // 单选模式 - 每次只选中一个键帽
    if (selectedKeys.includes(keyId)) {
      // 如果已经选中，则取消选中
      setSelectedKeys([]);
    } else {
      // 选中当前键帽，取消其他键帽的选中状态
      setSelectedKeys([keyId]);
    }
    
    // 确保没有任何动画效果 - 只有静态的蓝色边框显示
  };

  // 根据布局渲染不同的键盘模板
  // 生成键帽遮罩 - 始终全覆盖键帽
  const renderKeyboardLayoutMask = (layoutId: string) => {
    // 渲染单个键帽的遮罩区域 - 覆盖整个键帽区域
    const renderKeycapMask = (x: number, y: number, width: number, height: number) => {
      // 使用整个键帽的外边界作为遮罩区域
      const bottomRadius = 0.5 * (width / 18.2); // 底部圆角
      
      return (
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          rx={bottomRadius}
          ry={bottomRadius}
          fill="white"
        />
      );
    };
    
    switch (layoutId) {
      case '108':
        return (
          <g>
            {/* 第一行 - ESC和功能键区（Y=15，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 15, 55, 55)} {/* ESC */}
            {/* F1-F4组 - 使用与实际键帽相同的坐标 */}
            {['F1','F2','F3','F4'].map((k,i) => renderKeycapMask(135+i*57, 15, 55, 55))}
            {/* F5-F8组 */}
            {['F5','F6','F7','F8'].map((k,i) => renderKeycapMask(393+i*57, 15, 55, 55))}
            {/* F9-F12组 - 调整位置与主键盘对齐 */}
            {['F9','F10','F11','F12'].map((k,i) => renderKeycapMask(648+i*57, 15, 55, 55))}
            {/* 右上功能键组 */}
            {renderKeycapMask(894, 15, 55, 55)} {/* PRT */}
            {renderKeycapMask(951, 15, 55, 55)} {/* SCR */}
            {renderKeycapMask(1008, 15, 55, 55)} {/* PAU */}
            
            {/* 第二行 - 数字键行（Y=100，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 100, 55, 55)} {/* ~ */}
            {renderKeycapMask(77, 100, 55, 55)} {/* 1 */}
            {renderKeycapMask(134, 100, 55, 55)} {/* 2 */}
            {renderKeycapMask(191, 100, 55, 55)} {/* 3 */}
            {renderKeycapMask(248, 100, 55, 55)} {/* 4 */}
            {renderKeycapMask(305, 100, 55, 55)} {/* 5 */}
            {renderKeycapMask(362, 100, 55, 55)} {/* 6 */}
            {renderKeycapMask(419, 100, 55, 55)} {/* 7 */}
            {renderKeycapMask(476, 100, 55, 55)} {/* 8 */}
            {renderKeycapMask(533, 100, 55, 55)} {/* 9 */}
            {renderKeycapMask(590, 100, 55, 55)} {/* 0 */}
            {renderKeycapMask(647, 100, 55, 55)} {/* - */}
            {renderKeycapMask(704, 100, 55, 55)} {/* = */}
            {renderKeycapMask(761, 100, 113, 55)} {/* BACKSPACE */}
            {/* 右侧导航键组 */}
            {renderKeycapMask(894, 100, 55, 55)} {/* INS */}
            {renderKeycapMask(951, 100, 55, 55)} {/* HOME */}
            {renderKeycapMask(1008, 100, 55, 55)} {/* PAGE UP */}
            
            {/* 第三行 - TAB QWERTY行（Y=157，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 157, 84, 55)} {/* TAB */}
            {['Q','W','E','R','T','Y','U','I','O','P'].map((k,i) => renderKeycapMask(106+i*57, 157, 55, 55))}
            {renderKeycapMask(676, 157, 55, 55)} {/* [ */}
            {renderKeycapMask(733, 157, 55, 55)} {/* ] */}
            {renderKeycapMask(790, 157, 84, 55)} {/* \ */}
            {/* 右侧导航键组 */}
            {renderKeycapMask(894, 157, 55, 55)} {/* DEL */}
            {renderKeycapMask(951, 157, 55, 55)} {/* END */}
            {renderKeycapMask(1008, 157, 55, 55)} {/* PAGE DOWN */}
            
            {/* 第四行 - CAPS ASDF行（Y=214，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 214, 99, 55)} {/* CAPS */}
            {['A','S','D','F','G','H','J','K','L'].map((k,i) => renderKeycapMask(121+i*57, 214, 55, 55))}
            {renderKeycapMask(634, 214, 55, 55)} {/* ; */}
            {renderKeycapMask(691, 214, 55, 55)} {/* ' */}
            {renderKeycapMask(748, 214, 126, 55)} {/* ENTER */}
            
            {/* 第五行 - SHIFT ZXCV行（Y=271，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 271, 129, 55)} {/* LSHIFT */}
            {['Z','X','C','V','B','N','M'].map((k,i) => renderKeycapMask(151+i*57, 271, 55, 55))}
            {renderKeycapMask(550, 271, 55, 55)} {/* , */}
            {renderKeycapMask(607, 271, 55, 55)} {/* . */}
            {renderKeycapMask(664, 271, 55, 55)} {/* / */}
            {renderKeycapMask(721, 271, 154, 55)} {/* RSHIFT */}
            {renderKeycapMask(951, 271, 55, 55)} {/* UP */}
            
            {/* 第六行 - 控制键行（Y=328，与实际键帽坐标一致） */}
            {renderKeycapMask(20, 328, 71, 55)} {/* LCTRL */}
            {renderKeycapMask(93, 328, 71, 55)} {/* WIN */}
            {renderKeycapMask(166, 328, 71, 55)} {/* LALT */}
            {renderKeycapMask(239, 328, 346, 55)} {/* SPACE */}
            {renderKeycapMask(587, 328, 71, 55)} {/* RALT */}
            {renderKeycapMask(660, 328, 71, 55)} {/* FN */}
            {renderKeycapMask(731, 328, 71, 55)} {/* MENU */}
            {renderKeycapMask(804, 328, 71, 55)} {/* RCTRL */}
            {renderKeycapMask(894, 328, 55, 55)} {/* LEFT */}
            {renderKeycapMask(951, 328, 55, 55)} {/* DOWN */}
            {renderKeycapMask(1008, 328, 55, 55)} {/* RIGHT */}
            
            {/* 小键盘区域 - 与实际键帽坐标一致 */}
            {/* 第0行（Y=15） */}
            {renderKeycapMask(1081, 15, 55, 55)} {/* 空白 */}
            {renderKeycapMask(1138, 15, 55, 55)} {/* 空白 */}
            {renderKeycapMask(1195, 15, 55, 55)} {/* 空白 */}
            {renderKeycapMask(1252, 15, 55, 55)} {/* 空白 */}
            {/* 第一行（Y=100） */}
            {renderKeycapMask(1081, 100, 55, 55)} {/* NUM */}
            {renderKeycapMask(1138, 100, 55, 55)} {/* / */}
            {renderKeycapMask(1195, 100, 55, 55)} {/* * */}
            {renderKeycapMask(1252, 100, 55, 55)} {/* - */}
            {/* 第二行（Y=157） */}
            {renderKeycapMask(1081, 157, 55, 55)} {/* 7 */}
            {renderKeycapMask(1138, 157, 55, 55)} {/* 8 */}
            {renderKeycapMask(1195, 157, 55, 55)} {/* 9 */}
            {renderKeycapMask(1252, 157, 55, 112)} {/* + (竖排) */}
            {/* 第三行（Y=214） */}
            {renderKeycapMask(1081, 214, 55, 55)} {/* 4 */}
            {renderKeycapMask(1138, 214, 55, 55)} {/* 5 */}
            {renderKeycapMask(1195, 214, 55, 55)} {/* 6 */}
            {/* 第四行（Y=271） */}
            {renderKeycapMask(1081, 271, 55, 55)} {/* 1 */}
            {renderKeycapMask(1138, 271, 55, 55)} {/* 2 */}
            {renderKeycapMask(1195, 271, 55, 55)} {/* 3 */}
            {renderKeycapMask(1252, 271, 55, 113)} {/* ENTER (竖排) */}
            {/* 最后一行（Y=328） */}
            {renderKeycapMask(1081, 328, 113, 55)} {/* 0 (双倍宽度) */}
            {renderKeycapMask(1195, 328, 55, 55)} {/* . */}
          </g>
        );
        
      case '87':
      case '87-side':
        return (
          <g>
            {/* 第一行 - ESC + F键区（Y=10，与实际键帽坐标一致） */}
            {renderKeycapMask(10, 10, 48, 36)} {/* ESC */}
            {/* F1-F4 */}
            {['F1', 'F2', 'F3', 'F4'].map((key, i) => renderKeycapMask(85 + i * 55, 10, 48, 36))}
            {/* F5-F8 */}
            {['F5', 'F6', 'F7', 'F8'].map((key, i) => renderKeycapMask(315 + i * 55, 10, 48, 36))}
            {/* F9-F12 */}
            {['F9', 'F10', 'F11', 'F12'].map((key, i) => renderKeycapMask(545 + i * 55, 10, 48, 36))}
            {/* PRT SCR PAU */}
            {renderKeycapMask(775, 10, 48, 36)}
            {renderKeycapMask(830, 10, 48, 36)}
            {renderKeycapMask(885, 10, 48, 36)}
            
            {/* 第二行 - 数字键行（Y=55，与实际键帽坐标一致） */}
            {['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => renderKeycapMask(10 + i * 50, 55, 48, 40))}
            {renderKeycapMask(660, 55, 94, 40)} {/* BACKSPACE */}
            {/* INS HM UP */}
            {renderKeycapMask(791, 55, 48, 40)} {/* INS (调整坐标) */}
            {renderKeycapMask(846, 55, 48, 40)} {/* HOME (调整坐标) */}
            {renderKeycapMask(901, 55, 48, 40)} {/* PAGE UP (调整坐标) */}
            
            {/* 第三行 - TAB QWERTY行（Y=100，与实际键帽坐标一致） */}
            {renderKeycapMask(10, 100, 68, 40)} {/* TAB */}
            {/* QWERTY行 */}
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']'].map((key, i) => renderKeycapMask(83 + i * 50, 100, 48, 40))}
            {renderKeycapMask(683, 100, 55, 40)} {/* \ */}
            {/* DEL END DN */}
            {renderKeycapMask(775, 100, 48, 40)} {/* DEL */}
            {renderKeycapMask(830, 100, 48, 40)} {/* END */}
            {renderKeycapMask(885, 100, 48, 40)} {/* PAGE DOWN */}
            
            {/* 第四行 - CAPS ASDF行（Y=145，与实际键帽坐标一致） */}
            {renderKeycapMask(10, 145, 78, 40)} {/* CAPS */}
            {/* ASDF行 */}
            {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => renderKeycapMask(93 + i * 50, 145, 48, 40))}
            {renderKeycapMask(643, 145, 119, 40)} {/* ENTER (调整宽度) */}
            
            {/* 第五行 - SHIFT ZXCV行（Y=190，与实际键帽坐标一致） */}
            {renderKeycapMask(10, 190, 108, 40)} {/* LSHIFT */}
            {/* ZXCV行 */}
            {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => renderKeycapMask(123 + i * 50, 190, 48, 40))}
            {renderKeycapMask(623, 190, 115, 40)} {/* RSHIFT (调整宽度) */}
            {renderKeycapMask(830, 190, 48, 40)} {/* UP */}
            
            {/* 第六行 - 控制键行（Y=235，与实际键帽坐标一致） */}
            {renderKeycapMask(10, 235, 58, 40)} {/* LCTRL */}
            {renderKeycapMask(73, 235, 58, 40)} {/* WIN */}
            {renderKeycapMask(136, 235, 58, 40)} {/* LALT */}
            {renderKeycapMask(199, 235, 285, 40)} {/* SPACE (调整宽度) */}
            {renderKeycapMask(489, 235, 58, 40)} {/* RALT (调整坐标) */}
            {renderKeycapMask(552, 235, 58, 40)} {/* FN (调整坐标) */}
            {renderKeycapMask(615, 235, 58, 40)} {/* MENU (调整坐标) */}
            {renderKeycapMask(678, 235, 60, 40)} {/* RCTRL (调整坐标和宽度) */}
            {/* 方向键 */}
            {renderKeycapMask(775, 235, 48, 40)} {/* LEFT */}
            {renderKeycapMask(830, 235, 48, 40)} {/* DOWN */}
            {renderKeycapMask(885, 235, 48, 40)} {/* RIGHT */}
          </g>
        );
        
      default:
        return null;
    }
  };

  const renderKeyboardLayout = (layoutId: string) => {
    // 完全复制提供的立体键帽样式
    // 渲染特殊形状的键帽（空格键等梯形键帽）
    // 渲染特殊形状的键帽（空格键等梯形键帽）
    const renderSpecialKeycap = (
      x: number,
      y: number,
      width: number,
      height: number,
      main: string,
      keyId: string,
      type: 'space' | 'tab' | 'enter' | 'vertical' | 'double-width',
      fontSize: number = 14
    ) => {
      const isSelected = selectedKeys.includes(keyId);
      const isEditing = selectedKeycap === keyId;
      const customization = getKeycapCustomization(keyId);
      
      // 像素与mm转换比例（基于标准键帽55px = 18.2mm）
      const mmToPixel = 55 / 18.2; // 1mm = 3.02像素
      
      // 根据按键类型设置不同的参数
      let horizontalMarginMm: number, topMarginMm: number, bottomMarginMm: number;
      let bottomRadiusMm: number, topRadiusMm: number;
      let topWidthMm: number, topHeightMm: number;
      
      // Tab键、\键、Caps键、左Shift键、空格键和底部控制键的特殊设置
      if (type === 'tab' || keyId === 'key-backslash') {
        // 顶面：21.28mm × 13.85mm
        topWidthMm = 21.28;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (27.7 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (keyId.includes('caps')) {
        // Caps键特殊设置：底座32.65mm × 18.2mm，顶面26.23mm × 13.85mm
        topWidthMm = 26.23;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (32.65 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (keyId === 'key-lshift') {
        // 左Shift键特殊设置：底座42.65mm × 18.2mm，顶面36.13mm × 13.85mm
        topWidthMm = 36.13;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (42.65 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (keyId === 'key-rshift') {
        // 右Shift键特殊设置：底座50.85mm × 18.2mm，顶面44.43mm × 13.85mm
        topWidthMm = 44.43;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (50.85 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (keyId === 'key-backspace' || keyId === 'key-num0') {
        // Backspace键和小键盘0键特殊设置：底座37.2mm × 18.2mm，顶面30.78mm × 13.85mm
        topWidthMm = 30.78;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (37.2 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (type === 'space' || keyId === 'key-space' || keyId.endsWith('-space')) {
        // 空格键特殊设置：底座114.45mm × 18.2mm，顶面108mm × 13.85mm
        topWidthMm = 108;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (114.45 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (keyId === 'key-lctrl' || keyId === 'key-lwin' || keyId === 'key-lalt' || 
                 keyId === 'key-ralt' || keyId === 'key-fn' || keyId === 'key-menu' || keyId === 'key-rctrl') {
        // 底部控制键特殊设置：底座23.45mm × 18.2mm，顶面17.03mm × 13.85mm
        topWidthMm = 17.03;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (23.45 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (type === 'vertical' || keyId === 'key-numadd' || keyId === 'key-numenter') {
        // 小键盘垂直键特殊设置：底座18.2mm × 37.2mm，顶面13.85mm × 32.85mm
        topWidthMm = 13.85;
        topHeightMm = 32.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 37.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (18.2 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else if (type === 'enter' || keyId.includes('enter')) {
        // 回车键特殊设置：底座41.75mm × 18.2mm，顶面35.33mm × 13.85mm
        topWidthMm = 35.33;
        topHeightMm = 13.85;
        // 顶面居中靠上，距离顶部0.77mm
        topMarginMm = 0.77;
        bottomMarginMm = 18.2 - topHeightMm - topMarginMm; // 计算底部间距
        horizontalMarginMm = (41.75 - topWidthMm) / 2; // 水平居中
        // 圆角
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      } else {
        // 其他特殊键使用标准设置
        // 标准键帽规格：18.2mm × 18.2mm底座，11.78mm × 13.85mm顶面
        // 间距：水平3.21mm，顶部0.77mm，底部3.58mm
        topWidthMm = 11.78;
        topHeightMm = 13.85;
        horizontalMarginMm = 3.21;
        topMarginMm = 0.77;
        bottomMarginMm = 3.58;
        bottomRadiusMm = 0.5;
        topRadiusMm = 1.62;
      }
      
      // 转换为像素
      const horizontalMargin = horizontalMarginMm * mmToPixel;
      const topMargin = topMarginMm * mmToPixel;
      const bottomMargin = bottomMarginMm * mmToPixel;
      
      // 计算顶面尺寸和位置
      let topWidth, topHeight, topOffsetX, topOffsetY;
      
      if (type === 'tab' || keyId === 'key-backslash' || keyId.includes('caps') || keyId === 'key-lshift' || keyId === 'key-rshift' ||
          keyId === 'key-backspace' || keyId === 'key-num0' ||
          type === 'space' || keyId === 'key-space' || keyId.endsWith('-space') || 
          keyId === 'key-lctrl' || keyId === 'key-lwin' || keyId === 'key-lalt' || 
          keyId === 'key-ralt' || keyId === 'key-fn' || keyId === 'key-menu' || keyId === 'key-rctrl' ||
          type === 'enter' || keyId.includes('enter') ||
          type === 'vertical' || keyId === 'key-numadd' || keyId === 'key-numenter') {
        // 使用mm精确值
        topWidth = topWidthMm * mmToPixel;
        topHeight = topHeightMm * mmToPixel;
        topOffsetX = horizontalMargin;
        topOffsetY = topMargin;
      } else {
        // 标准计算方式
        topWidth = width - 2 * horizontalMargin;
        topHeight = height - topMargin - bottomMargin;
        topOffsetX = horizontalMargin;
        topOffsetY = topMargin;
      }
      
      const bottomRadius = bottomRadiusMm * mmToPixel;
      const topRadius = topRadiusMm * mmToPixel;
      
      // 生成唯一的clipPath ID
      const clipPathId = `keycap-clip-${keyId.replace(/[^a-zA-Z0-9]/g, '')}`;
      
      return (
        <g
          key={keyId}
          transform={`translate(${x},${y})`}
          className="keycap-group cursor-pointer"
          data-key={keyId}
          onClick={() => handleKeyClick(keyId)}
          onDragOver={handleKeycapDragOver}
          onDrop={(e) => handleKeycapDrop(e, keyId)}
          style={{ pointerEvents: 'auto' }}
        >
          {/* 定义裁剪路径 - 用于限制贴图在键帽面内 */}
          <defs>
            <clipPath id={clipPathId}>
              <rect
                x={topOffsetX} y={topOffsetY}
                width={topWidth} height={topHeight}
                rx={topRadius} ry={topRadius}
              />
            </clipPath>
          </defs>
          
          {/* 底部基座 - 透明填充 */}
          <rect
            x={0} y={0}
            width={width} height={height}
            fill="transparent"
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1.2 : isEditing ? 1.5 : 0.8}
            rx={bottomRadius} ry={bottomRadius}
          />
          
          {/* 背景贴图 - 根据覆盖模式渲染 */}
          {customization.backgroundImage && customization.coverageMode === 'full-keycap' && (
            <image
              x={customization.backgroundPosition.x}
              y={customization.backgroundPosition.y}
              width={width * customization.backgroundScale}
              height={height * customization.backgroundScale}
              href={customization.backgroundImage}
              opacity={customization.backgroundOpacity}
              preserveAspectRatio="xMidYMid slice"
            />
          )}

          {/* 背景贴图 - 仅顶面模式 */}
          {customization.backgroundImage && customization.coverageMode === 'top-only' && (
            <image
              x={topOffsetX + customization.backgroundPosition.x}
              y={topOffsetY + customization.backgroundPosition.y}
              width={topWidth * customization.backgroundScale}
              height={topHeight * customization.backgroundScale}
              href={customization.backgroundImage}
              opacity={customization.backgroundOpacity}
              clipPath={`url(#${clipPathId})`}
              preserveAspectRatio="xMidYMid slice"
            />
          )}
          
          {/* 顶部键帽面 - 透明填充 */}
          <rect
            x={topOffsetX} y={topOffsetY}
            width={topWidth} height={topHeight}
            fill="transparent"
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1.2 : isEditing ? 1.5 : 0.8}
            rx={topRadius} ry={topRadius}
          />
          
          {/* 8条连接线 - 底部相邻圆角端点与上层圆角端点连接 */}
          {/* 左上角 - 水平方向端点 */}
          <line
            x1={bottomRadius} y1={0}
            x2={topOffsetX + topRadius} y2={topOffsetY}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左上角 - 垂直方向端点 */}
          <line
            x1={0} y1={bottomRadius}
            x2={topOffsetX} y2={topOffsetY + topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右上角 - 水平方向端点 */}
          <line
            x1={width - bottomRadius} y1={0}
            x2={topOffsetX + topWidth - topRadius} y2={topOffsetY}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右上角 - 垂直方向端点 */}
          <line
            x1={width} y1={bottomRadius}
            x2={topOffsetX + topWidth} y2={topOffsetY + topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右下角 - 水平方向端点 */}
          <line
            x1={width - bottomRadius} y1={height}
            x2={topOffsetX + topWidth - topRadius} y2={topOffsetY + topHeight}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右下角 - 垂直方向端点 */}
          <line
            x1={width} y1={height - bottomRadius}
            x2={topOffsetX + topWidth} y2={topOffsetY + topHeight - topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左下角 - 水平方向端点 */}
          <line
            x1={bottomRadius} y1={height}
            x2={topOffsetX + topRadius} y2={topOffsetY + topHeight}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左下角 - 垂直方向端点 */}
          <line
            x1={0} y1={height - bottomRadius}
            x2={topOffsetX} y2={topOffsetY + topHeight - topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 文字内容 - 放在顶部键帽面上 */}
          <text
            x={width/2} y={topOffsetY + topHeight/2}
            textAnchor="middle"
            fontFamily={customization.fontFamily}
            fontWeight={customization.fontWeight}
            fontSize={customization.fontSize}
            fill={customization.textColor}
            fontStyle={customization.textStyle}
            textDecoration={customization.textDecoration}
            dominantBaseline="middle"
          >{main}</text>
          
          {/* 编辑状态指示器 */}
          {isEditing && (
            <g>
              <circle
                cx={width - 8} cy={8}
                r={4}
                fill="#ff6b35"
                stroke="#ffffff"
                strokeWidth={1}
              />
              <text
                x={width - 8} y={8}
                textAnchor="middle"
                fontSize={6}
                fill="#ffffff"
                dominantBaseline="middle"
                fontWeight="bold"
              >E</text>
            </g>
          )}
        </g>
      );
    };

    const renderOutlineKeycap = (
      x: number,
      y: number,
      width: number,
      height: number,
      main: string,
      keyId: string,
      fontSize: number = 14,
      sub?: string,
      subFontSize: number = 10,
      rotate?: number
    ) => {
      const isSelected = selectedKeys.includes(keyId);
      const isEditing = selectedKeycap === keyId;
      const customization = getKeycapCustomization(keyId);
      
      // 根据技术图精确规格，使用mm单位确保一致性
      // 底座：18.2mm × 18.2mm（正方形）
      // 顶面：11.78mm × 13.85mm（长方形）
      // 间距：水平3.21mm，顶部0.77mm，底部3.58mm
      
      // 像素与mm转换比例（基于标准键帽55px = 18.2mm）
      const mmToPixel = 55 / 18.2; // 1mm = 3.02像素
      
      // 使用固定的mm值，确保所有键帽间距完全一致
      const horizontalMarginMm = 3.21;   // 左右各3.21mm间距
      const topMarginMm = 0.77;          // 顶部0.77mm间距
      const bottomMarginMm = 3.58;       // 底部3.58mm间距
      
      const horizontalMargin = horizontalMarginMm * mmToPixel;   // 转换为像素
      const topMargin = topMarginMm * mmToPixel;                 // 转换为像素
      const bottomMargin = bottomMarginMm * mmToPixel;           // 转换为像素
      
      const topWidth = width - 2 * horizontalMargin;     // 顶面宽度 = 底座宽度 - 左右间距
      const topHeight = height - topMargin - bottomMargin; // 顶面高度 = 底座高度 - 上下间距
      const topOffsetX = horizontalMargin;    // 水平偏移 = 左侧间距
      const topOffsetY = topMargin;           // 垂直偏移 = 顶部间距
      
      // 圆角半径使用mm单位
      const bottomRadiusMm = 0.5;    // 底座圆角0.5mm
      const topRadiusMm = 1.62;      // 顶面圆角1.62mm
      
      const bottomRadius = bottomRadiusMm * mmToPixel;   // 转换为像素
      const topRadius = topRadiusMm * mmToPixel;         // 转换为像素
      
      // 生成唯一的clipPath ID
      const clipPathId = `keycap-clip-${keyId.replace(/[^a-zA-Z0-9]/g, '')}`;
      
      return (
        <g
          key={keyId}
          transform={`translate(${x},${y})${rotate ? ` rotate(${rotate},${width/2},${height/2})` : ''}`}
          className="keycap-group cursor-pointer"
          data-key={keyId}
          onClick={() => handleKeyClick(keyId)}
          onDragOver={handleKeycapDragOver}
          onDrop={(e) => handleKeycapDrop(e, keyId)}
          style={{ pointerEvents: 'auto' }}
        >
          {/* 定义裁剪路径 - 用于限制贴图在键帽面内 */}
          <defs>
            <clipPath id={clipPathId}>
              <rect
                x={topOffsetX} y={topOffsetY}
                width={topWidth} height={topHeight}
                rx={topRadius} ry={topRadius}
              />
            </clipPath>
          </defs>
          
          {/* 底部基座 - 18.2mm正方形 - 透明填充 */}
          <rect
            x={0} y={0}
            width={width} height={height}
            fill="transparent"
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1.2 : isEditing ? 1.5 : 0.8}
            rx={bottomRadius} ry={bottomRadius}
          />
          
          {/* 背景贴图 - 根据覆盖模式渲染 */}
          {customization.backgroundImage && customization.coverageMode === 'full-keycap' && (
            <image
              x={customization.backgroundPosition.x}
              y={customization.backgroundPosition.y}
              width={width * customization.backgroundScale}
              height={height * customization.backgroundScale}
              href={customization.backgroundImage}
              opacity={customization.backgroundOpacity}
              preserveAspectRatio="xMidYMid slice"
            />
          )}

          {/* 背景贴图 - 仅顶面模式 */}
          {customization.backgroundImage && customization.coverageMode === 'top-only' && (
            <image
              x={topOffsetX + customization.backgroundPosition.x}
              y={topOffsetY + customization.backgroundPosition.y}
              width={topWidth * customization.backgroundScale}
              height={topHeight * customization.backgroundScale}
              href={customization.backgroundImage}
              opacity={customization.backgroundOpacity}
              clipPath={`url(#${clipPathId})`}
              preserveAspectRatio="xMidYMid slice"
            />
          )}
          
          {/* 顶部键帽面 - 11.78mm × 13.85mm长方形 - 透明填充 */}
          <rect
            x={topOffsetX} y={topOffsetY}
            width={topWidth} height={topHeight}
            fill="transparent"
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1.2 : isEditing ? 1.5 : 0.8}
            rx={topRadius} ry={topRadius}
          />
          
          {/* 8条连接线 - 底部相邻圆角端点与上层圆角端点连接 */}
          {/* 左上角 - 水平方向端点 */}
          <line
            x1={bottomRadius} y1={0}
            x2={topOffsetX + topRadius} y2={topOffsetY}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左上角 - 垂直方向端点 */}
          <line
            x1={0} y1={bottomRadius}
            x2={topOffsetX} y2={topOffsetY + topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右上角 - 水平方向端点 */}
          <line
            x1={width - bottomRadius} y1={0}
            x2={topOffsetX + topWidth - topRadius} y2={topOffsetY}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右上角 - 垂直方向端点 */}
          <line
            x1={width} y1={bottomRadius}
            x2={topOffsetX + topWidth} y2={topOffsetY + topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右下角 - 水平方向端点 */}
          <line
            x1={width - bottomRadius} y1={height}
            x2={topOffsetX + topWidth - topRadius} y2={topOffsetY + topHeight}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 右下角 - 垂直方向端点 */}
          <line
            x1={width} y1={height - bottomRadius}
            x2={topOffsetX + topWidth} y2={topOffsetY + topHeight - topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左下角 - 水平方向端点 */}
          <line
            x1={bottomRadius} y1={height}
            x2={topOffsetX + topRadius} y2={topOffsetY + topHeight}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 左下角 - 垂直方向端点 */}
          <line
            x1={0} y1={height - bottomRadius}
            x2={topOffsetX} y2={topOffsetY + topHeight - topRadius}
            stroke={isSelected ? '#2196f3' : isEditing ? '#ff6b35' : '#666666'}
            strokeWidth={isSelected ? 1 : isEditing ? 1.2 : 0.8}
          />
          
          {/* 文字内容 - 放在顶部键帽面上 */}
          {sub ? (
            // 上下两行排版（数字键）
            <>
              <text
                x={width/2} y={topOffsetY + topHeight*0.35}
                textAnchor="middle"
                fontFamily={customization.fontFamily}
                fontWeight={customization.fontWeight}
                fontSize={subFontSize}
                fill={customization.textColor}
                fontStyle={customization.textStyle}
                textDecoration={customization.textDecoration}
                dominantBaseline="middle"
              >{sub}</text>
              <text
                x={width/2} y={topOffsetY + topHeight*0.65}
                textAnchor="middle"
                fontFamily={customization.fontFamily}
                fontWeight={customization.fontWeight}
                fontSize={customization.fontSize}
                fill={customization.textColor}
                fontStyle={customization.textStyle}
                textDecoration={customization.textDecoration}
                dominantBaseline="middle"
              >{main}</text>
            </>
          ) : (
            // 单行居中
            <text
              x={width/2} y={topOffsetY + topHeight/2}
              textAnchor="middle"
              fontFamily={customization.fontFamily}
              fontWeight={customization.fontWeight}
              fontSize={customization.fontSize}
              fill={customization.textColor}
              fontStyle={customization.textStyle}
              textDecoration={customization.textDecoration}
              dominantBaseline="middle"
            >{main}</text>
          )}
          
          {/* 编辑状态指示器 */}
          {isEditing && (
            <g>
              <circle
                cx={width - 8} cy={8}
                r={4}
                fill="#ff6b35"
                stroke="#ffffff"
                strokeWidth={1}
              />
              <text
                x={width - 8} y={8}
                textAnchor="middle"
                fontSize={6}
                fill="#ffffff"
                dominantBaseline="middle"
                fontWeight="bold"
              >E</text>
            </g>
          )}
        </g>
      );
    };
    
    switch (layoutId) {
      case '108': // 108键版 - 紧凑布局，底部两行对齐
        return (
          <g>
            {/* 第一行 - ESC和功能键区（Y=15，统一高度） */}
            {renderOutlineKeycap(20, 15, 55, 55, 'ESC', 'key-esc', 12)}
            {/* F1-F4组 - ESC与F1间距19.8mm，组内键帽间距0.8mm */}
            {['F1','F2','F3','F4'].map((k,i)=>renderOutlineKeycap(135+i*57,15,55,55,k,`key-${k.toLowerCase()}`,12))}
            {/* F5-F8组 - F4与F5间距10.3mm，组内键帽间距0.8mm */}
            {['F5','F6','F7','F8'].map((k,i)=>renderOutlineKeycap(393+i*57,15,55,55,k,`key-${k.toLowerCase()}`,12))}
            {/* F9-F12组 - F8与F9间距调整后约10mm，组内键帽间距0.8mm */}
            {['F9','F10','F11','F12'].map((k,i)=>renderOutlineKeycap(648+i*57,15,55,55,k,`key-${k.toLowerCase()}`,12))}
            {/* 右上功能键组 - F12与PRT间距5.8mm，PRT/SCR/PAU之间间距0.8mm */}
            {renderOutlineKeycap(894,15,55,55,'PRT','key-prt',11)}
            {renderOutlineKeycap(951,15,55,55,'SCR','key-scr',11)}
            {renderOutlineKeycap(1008,15,55,55,'PAU','key-pau',11)}
            
            {/* 第二行 - 数字键行（Y=100，第一排与第二排间距9.8mm，键帽间距0.8mm）*/}
            {renderOutlineKeycap(20,100,55,55,'~','key-grave',13,'`',10)}
            {renderOutlineKeycap(77,100,55,55,'!','key-1',13,'1',10)}
            {renderOutlineKeycap(134,100,55,55,'@','key-2',13,'2',10)}
            {renderOutlineKeycap(191,100,55,55,'#','key-3',13,'3',10)}
            {renderOutlineKeycap(248,100,55,55,'$','key-4',13,'4',10)}
            {renderOutlineKeycap(305,100,55,55,'%','key-5',13,'5',10)}
            {renderOutlineKeycap(362,100,55,55,'^','key-6',13,'6',10)}
            {renderOutlineKeycap(419,100,55,55,'&','key-7',13,'7',10)}
            {renderOutlineKeycap(476,100,55,55,'*','key-8',13,'8',10)}
            {renderOutlineKeycap(533,100,55,55,'(','key-9',13,'9',10)}
            {renderOutlineKeycap(590,100,55,55,')','key-0',13,'0',10)}
            {renderOutlineKeycap(647,100,55,55,'_','key-minus',13,'-',10)}
            {renderOutlineKeycap(704,100,55,55,'+','key-equal',13,'=',10)}
            {renderSpecialKeycap(761,100,113,55,'BACK','key-backspace','double-width',11)}
            {/* 右侧导航键组（3×3布局） */}
            {renderOutlineKeycap(894,100,55,55,'INS','key-ins',11)}
            {renderOutlineKeycap(951,100,55,55,'HM','key-home',10)}
            {renderOutlineKeycap(1008,100,55,55,'UP','key-pageup',10)}
            
            {/* 第三行 - TAB QWERTY行（Y=157，行间距0.8mm） */}
            {renderSpecialKeycap(20,157,84,55,'TAB','key-tab','tab',12)}
            {['Q','W','E','R','T','Y','U','I','O','P'].map((k,i)=>renderOutlineKeycap(106+i*57,157,55,55,k,`key-${k.toLowerCase()}`,13))}
            {renderOutlineKeycap(676,157,55,55,'[','key-lbracket',13)}
            {renderOutlineKeycap(733,157,55,55,']','key-rbracket',13)}
            {renderSpecialKeycap(790,157,84,55,'\\','key-backslash','double-width',13)}
            {/* 右侧导航键组（3×3布局） */}
            {renderOutlineKeycap(894,157,55,55,'DEL','key-del',11)}
            {renderOutlineKeycap(951,157,55,55,'END','key-end',11)}
            {renderOutlineKeycap(1008,157,55,55,'DN','key-pagedown',10)}
            
            {/* 第四行 - CAPS ASDF行（Y=214，行间距0.8mm） */}
            {renderSpecialKeycap(20,214,99,55,'CAPS','key-caps','double-width',12)}
            {['A','S','D','F','G','H','J','K','L'].map((k,i)=>renderOutlineKeycap(121+i*57,214,55,55,k,`key-${k.toLowerCase()}`,13))}
            {renderOutlineKeycap(634,214,55,55,';','key-semicolon',13)}
            {renderOutlineKeycap(691,214,55,55,"'",'key-quote',13)}
            {renderSpecialKeycap(748,214,126,55,'ENTER','key-enter','enter',12)}
            
            {/* 第五行 - SHIFT ZXCV行（Y=271，行间距0.8mm） */}
            {renderSpecialKeycap(20,271,129,55,'SHIFT','key-lshift','double-width',12)}
            {['Z','X','C','V','B','N','M'].map((k,i)=>renderOutlineKeycap(151+i*57,271,55,55,k,`key-${k.toLowerCase()}`,13))}
            {renderOutlineKeycap(550,271,55,55,',','key-comma',13)}
            {renderOutlineKeycap(607,271,55,55,'.','key-period',13)}
            {renderOutlineKeycap(664,271,55,55,'/','key-slash',13)}
            {renderSpecialKeycap(721,271,154,55,'SHIFT','key-rshift','double-width',12)}
            {/* 上方向键（3×3布局中间位置） */}
            {renderOutlineKeycap(951,271,55,55,'↑','key-up',16)}
            
            {/* 第六行 - 控制键行（Y=328，行间距0.8mm，调整空格键宽度对齐） */}
            {renderSpecialKeycap(20,328,71,55,'CTRL','key-lctrl','double-width',11)}
            {renderSpecialKeycap(93,328,71,55,'WIN','key-lwin','double-width',11)}
            {renderSpecialKeycap(166,328,71,55,'ALT','key-lalt','double-width',11)}
            {renderSpecialKeycap(239,328,346,55,'SPACE','key-space','space',12)}
            {renderSpecialKeycap(587,328,71,55,'ALT','key-ralt','double-width',11)}
            {renderSpecialKeycap(660,328,71,55,'FN','key-fn','double-width',11)}
            {renderSpecialKeycap(731,328,71,55,'MENU','key-menu','double-width',10)}
            {renderSpecialKeycap(804,328,71,55,'CTRL','key-rctrl','double-width',11)}
            {/* 方向键组（3×3布局） */}
            {renderOutlineKeycap(894,328,55,55,'←','key-left',16)}
            {renderOutlineKeycap(951,328,55,55,'↓','key-down',16)}
            {renderOutlineKeycap(1008,328,55,55,'→','key-right',16)}
            
            {/* 小键盘区域 - 4×5标准布局，键帽间距0.8mm，与PAU间距5.8mm */}
            {/* 第0行 - 空白键帽（Y=15，与主键盘第一行对齐） */}
            {renderOutlineKeycap(1081,15,55,55,'','key-numblank1',10)}
            {renderOutlineKeycap(1138,15,55,55,'','key-numblank2',10)}
            {renderOutlineKeycap(1195,15,55,55,'','key-numblank3',10)}
            {renderOutlineKeycap(1252,15,55,55,'','key-numblank4',10)}
            {/* 第一行（Y=100，与主键盘第二行对齐） */}
            {renderOutlineKeycap(1081,100,55,55,'NUM','key-numlock',11)}
            {renderOutlineKeycap(1138,100,55,55,'/','key-numdiv',13)}
            {renderOutlineKeycap(1195,100,55,55,'*','key-nummul',13)}
            {renderOutlineKeycap(1252,100,55,55,'-','key-numsub',13)}
            {/* 第二行（Y=157，与主键盘第三行对齐） */}
            {renderOutlineKeycap(1081,157,55,55,'7','key-num7',13)}
            {renderOutlineKeycap(1138,157,55,55,'8','key-num8',13)}
            {renderOutlineKeycap(1195,157,55,55,'9','key-num9',13)}
            {/* 小键盘加号（竖排） - 修正高度对齐 */}
            {renderSpecialKeycap(1252,157,55,112,'+','key-numadd','vertical',15)}
            {/* 第三行（Y=214，与主键盘第四行对齐） */}
            {renderOutlineKeycap(1081,214,55,55,'4','key-num4',13)}
            {renderOutlineKeycap(1138,214,55,55,'5','key-num5',13)}
            {renderOutlineKeycap(1195,214,55,55,'6','key-num6',13)}
            {/* 第四行（Y=271，与主键盘第五行对齐） */}
            {renderOutlineKeycap(1081,271,55,55,'1','key-num1',13)}
            {renderOutlineKeycap(1138,271,55,55,'2','key-num2',13)}
            {renderOutlineKeycap(1195,271,55,55,'3','key-num3',13)}
            {/* 小键盘ENTER（竖排） - 修正高度对齐 */}
            {renderSpecialKeycap(1252,271,55,113,'ENT','key-numenter','vertical',10)}
            {/* 最后一行（Y=328，与主键盘第六行对齐） */}
            {renderSpecialKeycap(1081,328,113,55,'0','key-num0','double-width',13)}
            {renderOutlineKeycap(1195,328,55,55,'.','key-numdot',13)}
          </g>
        );

      case '87': // 87键版 - 紧凑布局，底部两行对齐
        return (
          <g>
            {/* 第一行 - ESC和功能键区（Y=10） */}
            {renderOutlineKeycap(10, 10, 48, 36, 'ESC', 'key-87-esc', 9)}
            
            {/* F1-F4 */}
            {['F1', 'F2', 'F3', 'F4'].map((key, i) => 
              renderOutlineKeycap(85 + i * 55, 10, 48, 36, key, `key-87-${key.toLowerCase()}`, 9)
            )}
            
            {/* F5-F8 */}
            {['F5', 'F6', 'F7', 'F8'].map((key, i) => 
              renderOutlineKeycap(315 + i * 55, 10, 48, 36, key, `key-87-${key.toLowerCase()}`, 9)
            )}
            
            {/* F9-F12 */}
            {['F9', 'F10', 'F11', 'F12'].map((key, i) => 
              renderOutlineKeycap(545 + i * 55, 10, 48, 36, key, `key-87-${key.toLowerCase()}`, 9)
            )}
            
            {/* PRT SCR PAU */}
            {renderOutlineKeycap(775, 10, 48, 36, 'PRT', 'key-87-prt', 8)}
            {renderOutlineKeycap(830, 10, 48, 36, 'SCR', 'key-87-scr', 8)}
            {renderOutlineKeycap(885, 10, 48, 36, 'PAU', 'key-87-pau', 8)}
            
            {/* 第二行 - 数字键区（Y=55，缩小间距） */}
            {['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => 
              renderOutlineKeycap(10 + i * 50, 55, 48, 40, key, `key-87-${key === '~' ? 'tilde' : key}`, 11)
            )}
            
            {/* BACKSPACE */}
            {renderSpecialKeycap(660, 55, 94, 40, 'BACK', 'key-87-backspace', 'double-width', 10)}
            
            {/* INS HM UP */}
            {renderOutlineKeycap(791, 55, 48, 40, 'INS', 'key-87-ins', 8)}
            {renderOutlineKeycap(846, 55, 48, 40, 'HM', 'key-87-home', 8)}
            {renderOutlineKeycap(901, 55, 48, 40, 'UP', 'key-87-pageup', 8)}
            
            {/* 第三行 - TAB QWERTY行（Y=100，缩小间距） */}
            {renderSpecialKeycap(10, 100, 68, 40, 'TAB', 'key-87-tab', 'tab', 9)}
            
            {/* QWERTY行 */}
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']'].map((key, i) => 
              renderOutlineKeycap(83 + i * 50, 100, 48, 40, key, `key-87-${key.toLowerCase()}`, 11)
            )}
            
            {/* 反斜杠 */}
            {renderSpecialKeycap(683, 100, 55, 40, '\\', 'key-87-backslash', 'double-width', 11)}
            
            {/* DEL END DN */}
            {renderOutlineKeycap(775, 100, 48, 40, 'DEL', 'key-87-del', 8)}
            {renderOutlineKeycap(830, 100, 48, 40, 'END', 'key-87-end', 8)}
            {renderOutlineKeycap(885, 100, 48, 40, 'DN', 'key-87-pagedown', 8)}
            
            {/* 第四行 - CAPS ASDF行（Y=145，缩小间距） */}
            {renderSpecialKeycap(10, 145, 78, 40, 'CAPS', 'key-87-caps', 'double-width', 9)}
            
            {/* ASDF行 */}
            {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => 
              renderOutlineKeycap(93 + i * 50, 145, 48, 40, key, `key-87-${key === ';' ? 'semicolon' : key === "'" ? 'quote' : key.toLowerCase()}`, 11)
            )}
            
            {/* ENTER */}
            {renderSpecialKeycap(643, 145, 119, 40, 'ENTER', 'key-87-enter', 'enter', 9)}
            
            {/* 第五行 - SHIFT ZXCV行（Y=190，缩小间距） */}
            {renderSpecialKeycap(10, 190, 108, 40, 'SHIFT', 'key-87-lshift', 'double-width', 9)}
            
            {/* ZXCV行 */}
            {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => 
              renderOutlineKeycap(123 + i * 50, 190, 48, 40, key, `key-87-${key === ',' ? 'comma' : key === '.' ? 'period' : key === '/' ? 'slash' : key.toLowerCase()}`, 11)
            )}
            
            {/* 右SHIFT - 调整宽度以对齐底部行 */}
            {renderSpecialKeycap(623, 190, 115, 40, 'SHIFT', 'key-87-rshift', 'double-width', 9)}
            
            {/* 上箭头 */}
            {renderOutlineKeycap(830, 190, 48, 40, '↑', 'key-87-up', 14)}
            
            {/* 第六行 - 控制键行（Y=235，缩小间距，调整对齐） */}
            {renderOutlineKeycap(10, 235, 58, 40, 'CTRL', 'key-87-lctrl', 8)}
            {renderOutlineKeycap(73, 235, 58, 40, 'WIN', 'key-87-win', 8)}
            {renderOutlineKeycap(136, 235, 58, 40, 'ALT', 'key-87-lalt', 8)}
            
            {/* 空格键 - 调整宽度对齐 */}
            {renderSpecialKeycap(199, 235, 285, 40, 'SPACE', 'key-87-space', 'space', 10)}
            
            {/* 右侧修饰键 - 调整位置对齐 */}
            {renderOutlineKeycap(489, 235, 58, 40, 'ALT', 'key-87-ralt', 8)}
            {renderOutlineKeycap(552, 235, 58, 40, 'FN', 'key-87-fn', 8)}
            {renderOutlineKeycap(615, 235, 58, 40, 'MENU', 'key-87-menu', 8)}
            {renderOutlineKeycap(678, 235, 60, 40, 'CTRL', 'key-87-rctrl', 8)}
            
            {/* 方向键 */}
            {renderOutlineKeycap(775, 235, 48, 40, '←', 'key-87-left', 14)}
            {renderOutlineKeycap(830, 235, 48, 40, '↓', 'key-87-down', 14)}
            {renderOutlineKeycap(885, 235, 48, 40, '→', 'key-87-right', 14)}
          </g>
        );

      case '87-side': // 87键侧刻版 - 侧面印刷
        return (
          <g>
            {/* ESC键 */}
            {renderOutlineKeycap(10, 10, 48, 36, 'ESC', 'key-87side-esc', 9)}
            
            {/* F1-F4 */}
            {['F1', 'F2', 'F3', 'F4'].map((key, i) => 
              renderOutlineKeycap(85 + i * 55, 10, 48, 36, key, `key-87side-${key.toLowerCase()}`, 9)
            )}
            
            {/* F5-F8 */}
            {['F5', 'F6', 'F7', 'F8'].map((key, i) => 
              renderOutlineKeycap(315 + i * 55, 10, 48, 36, key, `key-87side-${key.toLowerCase()}`, 9)
            )}
            
            {/* F9-F12 */}
            {['F9', 'F10', 'F11', 'F12'].map((key, i) => 
              renderOutlineKeycap(545 + i * 55, 10, 48, 36, key, `key-87side-${key.toLowerCase()}`, 9)
            )}
            
            {/* PRT SCR PAU */}
            {renderOutlineKeycap(775, 10, 48, 36, 'PRT', 'key-87side-prt', 8)}
            {renderOutlineKeycap(830, 10, 48, 36, 'SCR', 'key-87side-scr', 8)}
            {renderOutlineKeycap(885, 10, 48, 36, 'PAU', 'key-87side-pau', 8)}
            
            {/* 数字键区 - 侧刻版本，字符显示在键帽侧面（Y=55，缩小间距） */}
            {['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => {
              const x = 10 + i * 50;
              const y = 55;
              const keyId = `key-87side-${key === '~' ? 'tilde' : key}`;
              return (
                <g key={keyId}>
                  {/* 主键帽 */}
                  {renderOutlineKeycap(x, y, 48, 40, '', keyId, 11)}
                  {/* 侧刻字符 - 显示在键帽右侧 */}
                  <text
                    x={x + 44} y={y + 35}
                    textAnchor="middle"
                    fontFamily="Arial, sans-serif"
                    fontWeight="bold"
                    fontSize="8"
                    fill="#666666"
                    dominantBaseline="middle"
                  >{key}</text>
                </g>
              );
            })}
            
            {/* BACKSPACE */}
            {renderSpecialKeycap(660, 55, 94, 40, 'BACK', 'key-87side-backspace', 'double-width', 10)}
            
            {/* INS HM UP */}
            {renderOutlineKeycap(791, 55, 48, 40, 'INS', 'key-87side-ins', 8)}
            {renderOutlineKeycap(846, 55, 48, 40, 'HM', 'key-87side-home', 8)}
            {renderOutlineKeycap(901, 55, 48, 40, 'UP', 'key-87side-pageup', 8)}
            
            {/* TAB（Y=100，缩小间距） */}
            {renderSpecialKeycap(10, 100, 68, 40, 'TAB', 'key-87side-tab', 'tab', 9)}
            
            {/* QWERTY行 - 侧刻版本 */}
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']'].map((key, i) => {
              const x = 83 + i * 50;
              const y = 100;
              const keyId = `key-87side-${key.toLowerCase()}`;
              return (
                <g key={keyId}>
                  {/* 主键帽 */}
                  {renderOutlineKeycap(x, y, 48, 40, '', keyId, 11)}
                  {/* 侧刻字符 - 显示在键帽右侧 */}
                  <text
                    x={x + 44} y={y + 35}
                    textAnchor="middle"
                    fontFamily="Arial, sans-serif"
                    fontWeight="bold"
                    fontSize="8"
                    fill="#666666"
                    dominantBaseline="middle"
                  >{key}</text>
                </g>
              );
            })}
            
            {/* 反斜杠 */}
            {renderSpecialKeycap(683, 100, 55, 40, '\\', 'key-87side-backslash', 'double-width', 11)}
            
            {/* DEL END DN */}
            {renderOutlineKeycap(775, 100, 48, 40, 'DEL', 'key-87side-del', 8)}
            {renderOutlineKeycap(830, 100, 48, 40, 'END', 'key-87side-end', 8)}
            {renderOutlineKeycap(885, 100, 48, 40, 'DN', 'key-87side-pagedown', 8)}
            
            {/* CAPS LOCK（Y=145，缩小间距） */}
            {renderSpecialKeycap(10, 145, 78, 40, 'CAPS', 'key-87side-caps', 'double-width', 9)}
            
            {/* ASDF行 - 侧刻版本 */}
            {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => {
              const x = 93 + i * 50;
              const y = 145;
              const keyId = `key-87side-${key === ';' ? 'semicolon' : key === "'" ? 'quote' : key.toLowerCase()}`;
              return (
                <g key={keyId}>
                  {/* 主键帽 */}
                  {renderOutlineKeycap(x, y, 48, 40, '', keyId, 11)}
                  {/* 侧刻字符 - 显示在键帽右侧 */}
                  <text
                    x={x + 44} y={y + 35}
                    textAnchor="middle"
                    fontFamily="Arial, sans-serif"
                    fontWeight="bold"
                    fontSize="8"
                    fill="#666666"
                    dominantBaseline="middle"
                  >{key}</text>
                </g>
              );
            })}
            
            {/* ENTER */}
            {renderSpecialKeycap(643, 145, 119, 40, 'ENTER', 'key-87side-enter', 'enter', 9)}
            
            {/* 左SHIFT（Y=190，缩小间距） */}
            {renderSpecialKeycap(10, 190, 108, 40, 'SHIFT', 'key-87side-lshift', 'double-width', 9)}
            
            {/* ZXCV行 - 侧刻版本 */}
            {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => {
              const x = 123 + i * 50;
              const y = 190;
              const keyId = `key-87side-${key === ',' ? 'comma' : key === '.' ? 'period' : key === '/' ? 'slash' : key.toLowerCase()}`;
              return (
                <g key={keyId}>
                  {/* 主键帽 */}
                  {renderOutlineKeycap(x, y, 48, 40, '', keyId, 11)}
                  {/* 侧刻字符 - 显示在键帽右侧 */}
                  <text
                    x={x + 44} y={y + 35}
                    textAnchor="middle"
                    fontFamily="Arial, sans-serif"
                    fontWeight="bold"
                    fontSize="8"
                    fill="#666666"
                    dominantBaseline="middle"
                  >{key}</text>
                </g>
              );
            })}
            
            {/* 右SHIFT - 调整宽度以对齐底部行 */}
            {renderSpecialKeycap(623, 190, 115, 40, 'SHIFT', 'key-87side-rshift', 'double-width', 9)}
            
            {/* 上箭头 */}
            {renderOutlineKeycap(830, 190, 48, 40, '↑', 'key-87side-up', 14)}
            
            {/* 底部行（Y=235，缩小间距，调整对齐） */}
            {renderOutlineKeycap(10, 235, 58, 40, 'CTRL', 'key-87side-lctrl', 8)}
            {renderOutlineKeycap(73, 235, 58, 40, 'WIN', 'key-87side-win', 8)}
            {renderOutlineKeycap(136, 235, 58, 40, 'ALT', 'key-87side-lalt', 8)}
            
            {/* 空格键 - 调整宽度对齐 */}
            {renderSpecialKeycap(199, 235, 285, 40, 'SPACE', 'key-87side-space', 'space', 10)}
            
            {/* 右侧修饰键 - 调整位置对齐 */}
            {renderOutlineKeycap(489, 235, 58, 40, 'ALT', 'key-87side-ralt', 8)}
            {renderOutlineKeycap(552, 235, 58, 40, 'FN', 'key-87side-fn', 8)}
            {renderOutlineKeycap(615, 235, 58, 40, 'MENU', 'key-87side-menu', 8)}
            {renderOutlineKeycap(678, 235, 60, 40, 'CTRL', 'key-87side-rctrl', 8)}
            
            {/* 方向键 */}
            {renderOutlineKeycap(775, 235, 48, 40, '←', 'key-87side-left', 14)}
            {renderOutlineKeycap(830, 235, 48, 40, '↓', 'key-87side-down', 14)}
            {renderOutlineKeycap(885, 235, 48, 40, '→', 'key-87side-right', 14)}
          </g>
        );

      case '61': // 61键版 - 紧凑布局
        return (
          <g>
            {/* 数字键区 */}
            {['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => 
              renderOutlineKeycap(10 + i * 50, 10, 48, 40, key, `key-61-${key === '~' ? 'tilde' : key}`, 11)
            )}
            
            {/* BACKSPACE */}
            {renderSpecialKeycap(660, 10, 94, 40, 'BACK', 'key-61-backspace', 'double-width', 10)}
            
            {/* TAB */}
            {renderSpecialKeycap(10, 55, 68, 40, 'TAB', 'key-61-tab', 'tab', 9)}
            
            {/* QWERTY行 */}
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']'].map((key, i) => 
              renderOutlineKeycap(83 + i * 50, 55, 48, 40, key, `key-61-${key.toLowerCase()}`, 11)
            )}
            
            {/* 反斜杠 */}
            {renderSpecialKeycap(683, 55, 55, 40, '\\', 'key-61-backslash', 'double-width', 11)}
            
            {/* CAPS LOCK */}
            {renderSpecialKeycap(10, 100, 78, 40, 'CAPS', 'key-61-caps', 'double-width', 9)}
            
            {/* ASDF行 */}
            {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => 
              renderOutlineKeycap(93 + i * 50, 100, 48, 40, key, `key-61-${key === ';' ? 'semicolon' : key === "'" ? 'quote' : key.toLowerCase()}`, 11)
            )}
            
            {/* ENTER */}
            {renderSpecialKeycap(643, 100, 119, 40, 'ENTER', 'key-61-enter', 'enter', 9)}
            
            {/* 左SHIFT */}
            {renderSpecialKeycap(10, 145, 108, 40, 'SHIFT', 'key-61-lshift', 'double-width', 9)}
            
            {/* ZXCV行 */}
            {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => 
              renderOutlineKeycap(123 + i * 50, 145, 48, 40, key, `key-61-${key === ',' ? 'comma' : key === '.' ? 'period' : key === '/' ? 'slash' : key.toLowerCase()}`, 11)
            )}
            
            {/* 右SHIFT */}
            {renderSpecialKeycap(623, 145, 112, 40, 'SHIFT', 'key-61-rshift', 'double-width', 9)}
            
            {/* 底部行 */}
            {renderOutlineKeycap(10, 190, 58, 40, 'CTRL', 'key-61-lctrl', 8)}
            {renderOutlineKeycap(73, 190, 58, 40, 'WIN', 'key-61-win', 8)}
            {renderOutlineKeycap(136, 190, 58, 40, 'ALT', 'key-61-lalt', 8)}
            
            {/* 空格键 */}
            {renderSpecialKeycap(199, 190, 282, 40, 'SPACE', 'key-61-space', 'space', 10)}
            
            {/* 右侧修饰键 */}
            {renderOutlineKeycap(486, 190, 58, 40, 'ALT', 'key-61-ralt', 8)}
            {renderOutlineKeycap(549, 190, 58, 40, 'FN', 'key-61-fn', 8)}
            {renderOutlineKeycap(612, 190, 58, 40, 'CTRL', 'key-61-rctrl', 8)}
            
            {/* 方向键 - 紧凑排列 */}
            {renderOutlineKeycap(675, 145, 40, 38, '←', 'key-61-left', 12)}
            {renderOutlineKeycap(720, 145, 40, 38, '↑', 'key-61-up', 12)}
            {renderOutlineKeycap(720, 190, 40, 38, '↓', 'key-61-down', 12)}
            {renderOutlineKeycap(765, 145, 40, 38, '→', 'key-61-right', 12)}
          </g>
        );

      case '104': // 104键版 - 标准键盘 (类似108但略有差异)
        return renderKeyboardLayout('108'); // 暂时使用108键布局

      case '97': // 97键版 - 紧凑全尺寸
        return (
          <g>
            {/* ESC和F键行 - 更紧凑的间距 */}
            <g transform="translate(10,10)">
              <rect x="1" y="1" width="40" height="30" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="21" y="19" textAnchor="middle" fontSize="8" fill="#495057" fontWeight="600">ESC</text>
            </g>
            
            {/* F1-F12 紧凑排列 */}
            {['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'].map((key, i) => (
              <g key={key} transform={`translate(${55 + i * 42},10)`}>
                <rect x="1" y="1" width="40" height="30" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="21" y="19" textAnchor="middle" fontSize="8" fill="#495057" fontWeight="600">{key}</text>
              </g>
            ))}
            
            {/* DEL */}
            <g transform="translate(560,10)">
              <rect x="1" y="1" width="40" height="30" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="21" y="19" textAnchor="middle" fontSize="8" fill="#495057" fontWeight="600">DEL</text>
            </g>
            
            {/* 第二行 - 数字键 */}
            {['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => (
              <g key={key} transform={`translate(${10 + i * 42},50)`}>
                <rect x="1" y="1" width="40" height="35" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="21" y="22" textAnchor="middle" fontSize="10" fill="#495057" fontWeight="600">{key}</text>
              </g>
            ))}
            
            <g transform="translate(556,50)">
              <rect x="1" y="1" width="55" height="35" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="28" y="22" textAnchor="middle" fontSize="9" fill="#495057" fontWeight="600">BACK</text>
            </g>
            
            {/* 第三行 - QWERTY */}
            <g transform="translate(10,90)">
              <rect x="1" y="1" width="55" height="35" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="28" y="22" textAnchor="middle" fontSize="8" fill="#495057" fontWeight="600">TAB</text>
            </g>
            
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].map((key, i) => (
              <g key={key} transform={`translate(${70 + i * 42},90)`}>
                <rect x="1" y="1" width="40" height="35" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="21" y="22" textAnchor="middle" fontSize="10" fill="#495057" fontWeight="600">{key}</text>
              </g>
            ))}
            
            {/* 其余行类似... 这里为了简化显示主要按键 */}
            <g transform="translate(250,200)">
              <rect x="1" y="1" width="200" height="35" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="100" y="22" textAnchor="middle" fontSize="10" fill="#495057" fontWeight="600">97键布局预览</text>
            </g>
          </g>
        );

      case '98': // 98键版
      case '982U': // 982U键版
        return (
          <g>
            {/* 简化的98键布局显示 */}
            <g transform="translate(250,150)">
              <rect x="1" y="1" width="200" height="50" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="100" y="30" textAnchor="middle" fontSize="12" fill="#495057" fontWeight="600">98键布局</text>
              <text x="100" y="45" textAnchor="middle" fontSize="10" fill="#6c757d" fontWeight="400">紧凑式全尺寸键盘</text>
            </g>
          </g>
        );

      case '75': // 75键版
      case '74': // 74键版  
      case '夜魔75': // 夜魔75键版
        return (
          <g>
            {/* 75%布局 - 紧凑无间隙 */}
            <g transform="translate(250,150)">
              <rect x="1" y="1" width="200" height="50" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="100" y="30" textAnchor="middle" fontSize="12" fill="#495057" fontWeight="600">75%布局</text>
              <text x="100" y="45" textAnchor="middle" fontSize="10" fill="#6c757d" fontWeight="400">紧凑型布局，保留F键</text>
            </g>
          </g>
        );

      case '68': // 68键版
        return (
          <g>
            {/* 68键布局显示 */}
            <g transform="translate(250,150)">
              <rect x="1" y="1" width="200" height="50" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
              <text x="100" y="30" textAnchor="middle" fontSize="12" fill="#495057" fontWeight="600">68键布局</text>
              <text x="100" y="45" textAnchor="middle" fontSize="10" fill="#6c757d" fontWeight="400">65%布局加方向键</text>
            </g>
          </g>
        );

      case '小键盘': // 小键盘版
        return (
          <g>
            {/* 数字小键盘 */}
            <g transform="translate(250,50)">
              {/* NUM行 */}
              {['NUM', '/', '*', '-'].map((key, i) => (
                <g key={key} transform={`translate(${i * 50},0)`}>
                  <rect x="1" y="1" width="45" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                  <text x="23.5" y="24" textAnchor="middle" fontSize="9" fill="#495057" fontWeight="600">{key}</text>
                </g>
              ))}
              
              {/* 数字7-9 */}
              {['7', '8', '9'].map((key, i) => (
                <g key={key} transform={`translate(${i * 50},45)`}>
                  <rect x="1" y="1" width="45" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                  <text x="23.5" y="24" textAnchor="middle" fontSize="11" fill="#495057" fontWeight="600">{key}</text>
                </g>
              ))}
              
              {/* + 键 */}
              <g transform="translate(150,45)">
                <rect x="1" y="1" width="45" height="83" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="23.5" y="46" textAnchor="middle" fontSize="14" fill="#495057" fontWeight="600">+</text>
              </g>
              
              {/* 数字4-6 */}
              {['4', '5', '6'].map((key, i) => (
                <g key={key} transform={`translate(${i * 50},90)`}>
                  <rect x="1" y="1" width="45" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                  <text x="23.5" y="24" textAnchor="middle" fontSize="11" fill="#495057" fontWeight="600">{key}</text>
                </g>
              ))}
              
              {/* 数字1-3 */}
              {['1', '2', '3'].map((key, i) => (
                <g key={key} transform={`translate(${i * 50},135)`}>
                  <rect x="1" y="1" width="45" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                  <text x="23.5" y="24" textAnchor="middle" fontSize="11" fill="#495057" fontWeight="600">{key}</text>
                </g>
              ))}
              
              {/* ENTER */}
              <g transform="translate(150,135)">
                <rect x="1" y="1" width="45" height="83" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="23.5" y="46" textAnchor="middle" fontSize="9" fill="#495057" fontWeight="600">ENT</text>
              </g>
              
              {/* 数字0 */}
              <g transform="translate(0,180)">
                <rect x="1" y="1" width="95" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="48" y="24" textAnchor="middle" fontSize="11" fill="#495057" fontWeight="600">0</text>
              </g>
              
              {/* 小数点 */}
              <g transform="translate(100,180)">
                <rect x="1" y="1" width="45" height="38" rx="4" fill="transparent" stroke="#d1d5db" strokeWidth="1"/>
                <text x="23.5" y="27" textAnchor="middle" fontSize="14" fill="#495057" fontWeight="600">.</text>
              </g>
            </g>
          </g>
        );

      default: // 默认显示108键
        return renderKeyboardLayout('108');
    }
  };

  // 删除用户素材
  const deleteUserMaterial = async (materialId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return false;
      }

      const response = await fetch(`http://localhost:8080/api/materials/${materialId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`删除失败: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.code === 200) {
        // 从前端状态中移除
        setUploadedImages(prev => prev.filter(img => img.materialId !== materialId));
        console.log('素材删除成功:', materialId);
        return true;
      } else {
        throw new Error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('删除素材失败:', error);
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    }
  };

  // 从URL参数加载设计
  const loadDesignFromUrl = async () => {
    // 获取URL参数
    const searchParams = new URLSearchParams(window.location.search);
    const designId = searchParams.get('id');
    
    if (!designId) {
      console.log('没有设计ID参数，开始新设计');
      // 即使没有设计ID，也要加载用户素材
      await loadUserImages();
      return;
    }

    setIsLoadingDesign(true);
    setCurrentDesignId(designId);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('用户未登录，无法加载设计');
        return;
      }

      console.log('🔍 开始加载设计，设计ID：', designId);

      // 并行加载设计数据和用户素材
      const [designResponse, _] = await Promise.all([
        fetch(`http://localhost:8080/api/designs/${designId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        loadUserImages() // 同时加载用户素材
      ]);

      if (!designResponse.ok) {
        throw new Error(`加载设计失败: ${designResponse.status} ${designResponse.statusText}`);
      }

      const result = await designResponse.json();
      
      if (result.code === 200) {
        const design = result.data;
        console.log('🎉 设计加载成功:', design);

        // 恢复设计数据
        setDesignName(design.designName);
        
        // 解析设计数据
        try {
          const designData = JSON.parse(design.designData);
          console.log('📋 解析设计数据:', designData);

          // 恢复设计设置
          if (designData.settings) {
            setDesignSettings(designData.settings);
            console.log('⚙️ 恢复设计设置:', designData.settings);
          }

          // 恢复选中的键帽
          if (designData.selectedKeys) {
            setSelectedKeys(designData.selectedKeys);
            console.log('🔑 恢复选中键帽:', designData.selectedKeys);
          }

          // 恢复拖拽元素 - 需要等待用户素材加载完成
          if (designData.elements && designData.elements.length > 0) {
            console.log('🎨 恢复拖拽元素:', designData.elements);
            setDraggedElements(designData.elements);
          }

        } catch (parseError) {
          console.error('❌ 解析设计数据失败:', parseError);
        }

      } else {
        throw new Error(result.message || '加载设计失败');
      }

    } catch (error) {
      console.error('❌ 加载设计失败:', error);
      alert(`加载设计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoadingDesign(false);
    }
  };

  // 页面加载时检查URL参数
  useEffect(() => {
    loadDesignFromUrl();
  }, []);

  // 监听uploadedImages变化，恢复待处理的设计元素
  useEffect(() => {
    if (pendingDesignElements.length > 0 && uploadedImages.length > 0) {
      console.log('🔄 开始恢复待处理的设计元素');
      
      const updatedElements = pendingDesignElements.map((element: any) => {
        // 多种匹配策略
        let matchingImage = null;
        
        // 1. 优先通过materialId匹配
        if (element.materialId) {
          matchingImage = uploadedImages.find(img => img.materialId === element.materialId);
        }
        
        // 2. 通过文件名匹配
        if (!matchingImage && element.imageName) {
          matchingImage = uploadedImages.find(img => img.name === element.imageName);
        }
        
        // 3. 通过尺寸匹配
        if (!matchingImage && element.imageWidth && element.imageHeight) {
          matchingImage = uploadedImages.find(img => 
            img.width === element.imageWidth && img.height === element.imageHeight
          );
        }
        
        // 4. 通过文件名部分匹配
        if (!matchingImage && element.imageName) {
          matchingImage = uploadedImages.find(img => 
            img.name.includes(element.imageName.split('.')[0]) || 
            element.imageName.includes(img.name.split('.')[0])
          );
        }
        
        if (matchingImage) {
          console.log('✅ 找到匹配图片:', element.id, '->', matchingImage.name);
          return {
            ...element,
            imageUrl: matchingImage.url
          };
        } else {
          console.log('❌ 未找到匹配图片，移除元素:', element.id, element.imageName);
          return null; // 标记为删除
        }
      }).filter(Boolean); // 移除null元素
      
      setDraggedElements(updatedElements);
      setPendingDesignElements([]); // 清空待处理列表
      console.log('🎨 设计元素恢复完成，成功恢复:', updatedElements.length, '个元素');
    }
  }, [uploadedImages, pendingDesignElements]);

  if (currentView === 'preview') {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="bg-black/20 backdrop-blur-sm p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setCurrentView('design')}
                className="text-white hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-semibold text-white">3D预览 - {designName}</h1>
            </div>
          </div>
        </div>

        <div className="h-[calc(100vh-80px)] flex items-center justify-center">
          <div className="text-center">
            <div className="w-32 h-32 mx-auto bg-white/10 rounded-2xl flex items-center justify-center mb-4">
              <svg className="w-16 h-16 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">3D渲染预览</h2>
            <p className="text-gray-300 mb-6 max-w-md mx-auto">
              您的键帽设计正在3D环境中渲染。
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-white">
      {/* 加载状态遮罩 */}
      {isLoadingDesign && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="text-4xl mb-4">⏳</div>
            <div className="text-lg font-semibold text-gray-900 mb-2">正在加载设计...</div>
            <div className="text-sm text-gray-600">请稍候，正在恢复您的设计状态</div>
          </div>
        </div>
      )}

      {/* 顶部状态栏 */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 px-4 py-2 z-30 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => window.history.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <span>←</span>
              <span>返回</span>
            </button>
            
            {currentDesignId && (
              <div className="flex items-center space-x-2 text-sm">
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                  编辑模式
                </span>
                <span className="text-gray-600">设计ID: {currentDesignId.substring(0, 8)}...</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              {draggedElements.length > 0 && (
                <span>设计元素: {draggedElements.length}个</span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={saveDesign}
                disabled={isSaving}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all ${
                  isSaving
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                <span>{isSaving ? '⏳' : '💾'}</span>
                <span>{isSaving ? '保存中...' : currentDesignId ? '更新设计' : '保存到云端'}</span>
              </button>

              <button
                onClick={exportDesignAsSVG}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-all bg-blue-600 text-white hover:bg-blue-700"
              >
                <span>📄</span>
                <span>导出布局SVG</span>
              </button>

              <button
                onClick={exportKeycapsAsSeparateSVG}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-all bg-purple-600 text-white hover:bg-purple-700"
              >
                <span>🔲</span>
                <span>导出键帽排列</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域，添加顶部边距 */}
      <div className="flex flex-1 pt-16">
        {/* 左侧垂直工具栏 */}
        <div className="w-12 bg-gray-50 border-r border-gray-200 flex flex-col items-center py-4 h-full">
          <div className="flex flex-col items-center space-y-4">
            <button
              onClick={() => window.history.back()}
              className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center hover:bg-gray-200 transition-colors"
              title="返回"
            >
              ←
            </button>
            
            <div className="w-full h-px bg-gray-200"></div>
            
            <button
              onClick={() => !isUploading && fileInputRef.current?.click()}
              disabled={isUploading}
              className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                isUploading 
                  ? 'bg-gray-100 cursor-not-allowed opacity-50' 
                  : 'bg-blue-100 hover:bg-blue-200'
              }`}
              title={isUploading ? "上传中..." : "上传素材"}
            >
              {isUploading ? '⏳' : '📁'}
            </button>
            
            <button
              className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center hover:bg-gray-200 transition-colors"
              title="设置"
            >
              ⚙️
            </button>
            
            <button
              className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center hover:bg-gray-200 transition-colors"
              title="尺寸"
            >
              📐
            </button>
            
            <div className="w-full h-px bg-gray-200"></div>
            
            <button
              onClick={clearAllKeycapBackgrounds}
              className="w-8 h-8 bg-red-100 rounded flex items-center justify-center hover:bg-red-200 transition-colors"
              title="清除所有键帽背景"
            >
              🧹
            </button>
          </div>
          
          {/* 底部操作按钮 - 使用 flex-1 和 justify-end 确保对齐到底部 */}
          <div className="flex-1 flex flex-col justify-end items-center space-y-4 pb-0">
            <button
              onClick={saveDesign}
              disabled={isSaving}
              className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                isSaving
                  ? 'bg-gray-100 cursor-not-allowed opacity-50'
                  : 'bg-green-100 hover:bg-green-200'
              }`}
              title={isSaving ? "保存中..." : "保存到云端"}
            >
              {isSaving ? '⏳' : '💾'}
            </button>
            <button
              onClick={exportDesignAsSVG}
              className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center hover:bg-blue-200 transition-colors"
              title="导出布局SVG文件"
            >
              📄
            </button>
            <button
              onClick={exportKeycapsAsSeparateSVG}
              className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center hover:bg-purple-200 transition-colors"
              title="导出键帽排列SVG"
            >
              🔲
            </button>
            <button
              onClick={() => setCurrentView('preview')}
              className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center hover:bg-gray-200 transition-colors"
              title="3D预览"
            >
              👁️
            </button>
          </div>
        </div>

        {/* 左侧分类选择面板 */}
        <div className="w-64 bg-white border-r border-gray-200 overflow-y-auto h-full">
          <div className="p-4 h-full flex flex-col">
            {/* 搜索框 */}
            <div className="relative mb-6">
              <input
                type="text"
                placeholder="输入关键词搜索"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            {/* 键帽分类 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center justify-between">
                键帽
                <span className="text-blue-600 text-xs cursor-pointer">全部 &gt;</span>
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {KEYBOARD_LAYOUTS.map((layout) => (
                  <button
                    key={layout.id}
                    onClick={() => setDesignSettings(prev => ({ ...prev, layout: layout.id }))}
                    className={`p-3 text-left rounded-lg border transition-all ${
                      designSettings.layout === layout.id
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-sm">{layout.name}</div>
                    <div className="text-xs text-gray-500 mt-1">{layout.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 素材库 */}
            <div className="border-t pt-6 flex-1 flex flex-col">
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-gray-900">素材库</h3>
                  {materialLibraryView === 'materials' && (
                    <button
                      onClick={backToCategories}
                      className="text-xs text-gray-600 hover:text-gray-800 flex items-center space-x-1"
                    >
                      <span>←</span>
                      <span>返回</span>
                </button>
                  )}
                </div>
                
                {/* 素材来源选择 */}
                {materialLibraryView === 'categories' && (
                  <div className="space-y-3">
                    {/* 我的素材 */}
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📁</span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-blue-900">我的素材</div>
                          <div className="text-xs text-blue-700">{uploadedImages.length} 个素材</div>
                        </div>
              </div>
            </div>

                    {/* 素材库分类 */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900">素材库</h4>
                      </div>
                      
                      {/* 分类列表 - 垂直布局 */}
                      <div className="space-y-1">
                        {MATERIAL_CATEGORIES.map((category) => (
                          <button
                            key={category.id}
                            onClick={() => handleCategorySelect(category.id)}
                            className="w-full flex items-center justify-between p-2 text-left rounded-lg hover:bg-gray-50 transition-all group"
                          >
                            <div className="flex items-center space-x-2">
                              <span className="text-sm">{category.icon}</span>
                              <span className="text-sm text-gray-900">{category.name}</span>
                            </div>
                            <div className="flex items-center space-x-1 text-xs text-gray-500 group-hover:text-gray-700">
                              <span>全部</span>
                              <span>&gt;</span>
                            </div>
                </button>
                        ))}
              </div>
            </div>
                  </div>
                )}
                
                {/* 素材展示区域 */}
                {materialLibraryView === 'materials' && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">
                          {MATERIAL_CATEGORIES.find(c => c.id === selectedCategory)?.icon || '📦'}
                </span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {MATERIAL_CATEGORIES.find(c => c.id === selectedCategory)?.name || '素材'}
                          </div>
                          <div className="text-xs text-gray-600">
                            共找到 {filteredMaterials.length} 个素材
                          </div>
                        </div>
                      </div>
              </div>
              

                  </div>
                )}
              </div>
              
              {/* 快速上传按钮 */}
              <div className="mb-4">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  multiple
                  accept="image/*"
                  className="hidden"
                  disabled={isUploading}
                />
                <button
                  onClick={() => !isUploading && fileInputRef.current?.click()}
                  disabled={isUploading}
                  className={`w-full p-2 rounded-lg border transition-all duration-200 ${
                    isUploading 
                      ? 'border-gray-300 bg-gray-50 cursor-not-allowed' 
                      : 'border-blue-300 bg-blue-50 hover:border-blue-400 hover:bg-blue-100 cursor-pointer'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <div className="text-lg">{isUploading ? '⏳' : '📁'}</div>
                    <div className={`text-sm font-medium ${
                      isUploading ? 'text-gray-600' : 'text-blue-700'
                    }`}>
                      {isUploading ? '上传中...' : '+ 快速上传'}
                    </div>
                  </div>
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto">
                {/* 分类选择模式 - 显示用户素材 */}
                {materialLibraryView === 'categories' && (
                  <>
                {uploadedImages.length === 0 ? (
                  <div className="text-center py-6">
                        <div className="text-4xl mb-3">🎨</div>
                    <p className="text-sm text-gray-500 mb-2">
                          还没有素材
                        </p>
                        <p className="text-xs text-gray-400 mb-3">
                          点击上方按钮上传素材，或访问素材库选择
                        </p>
                        <div className="space-y-2">
                          <button
                            onClick={() => !isUploading && fileInputRef.current?.click()}
                            disabled={isUploading}
                            className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            上传素材
                          </button>
                        </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                  {/* 紧凑的网格布局 */}
                  <div className="grid grid-cols-2 gap-2">
                    {uploadedImages.map((image) => (
                      <div
                        key={image.id}
                              className={`relative group rounded-lg overflow-hidden transition-all duration-200 hover:shadow-sm border ${
                                image.id === 'test-image-001'
                                  ? 'border-orange-300 bg-orange-50 hover:border-orange-400'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                      >
                        {/* 测试图片标识 */}
                        {image.id === 'test-image-001' && (
                          <div className="absolute top-1 left-1 bg-orange-500 text-white text-xs px-1.5 py-0.5 rounded-full z-10 font-medium">
                            测试
                          </div>
                        )}
                        <img
                          src={image.url}
                          alt={image.name}
                          className="w-full h-20 object-cover cursor-grab active:cursor-grabbing select-none"
                          draggable={false}
                          onMouseDown={(e) => handleDragStart(e, image)}
                          style={{
                            userSelect: 'none',
                            WebkitUserSelect: 'none',
                            MozUserSelect: 'none',
                            msUserSelect: 'none',
                          }}
                        />
                        <div className="p-2">
                          <p className="text-xs text-gray-600 truncate font-medium">
                            {image.id === 'test-image-001' ? '🧪 ' : ''}{image.name}
                          </p>
                          {/* 显示图片信息 */}
                          {image.width && image.height && (
                            <p className="text-xs text-gray-400 mt-1">
                              {image.width}×{image.height}
                              {image.fileSize && ` · ${Math.round(image.fileSize / 1024)}KB`}
                              {image.id === 'test-image-001' && ' · 前端测试'}
                            </p>
                          )}
                                
                                {/* 应用按钮 */}
                                <div className="mt-2 flex flex-col gap-1">
                                  {/* 第一行：创建图层和应用键帽 */}
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() => applyMaterialToKeyboard(image)}
                                      className="flex-1 bg-blue-500 text-white text-xs px-2 py-1 rounded hover:bg-blue-600 transition-colors"
                                    >
                                      创建图层
                                    </button>
                                    <button
                                      onClick={() => applyMaterialToKeycaps(image)}
                                      className="flex-1 bg-green-500 text-white text-xs px-2 py-1 rounded hover:bg-green-600 transition-colors"
                                    >
                                      应用键帽
                                    </button>
                                  </div>
                                  {/* 第二行：覆盖 */}
                                  <button
                                    onClick={() => applyMaterialToKeyboardWithExtension(image)}
                                    className="w-full bg-purple-500 text-white text-xs px-2 py-1 rounded hover:bg-purple-600 transition-colors"
                                    title="创建图层并相对键帽间距扩展9.1mm (11.4倍键帽间距0.8mm)"
                                  >
                                    覆盖 (+9.1mm)
                                  </button>
                                </div>
                        </div>
                        
                        {/* 删除按钮 - 测试图片不显示删除按钮 */}
                        {image.materialId && image.id !== 'test-image-001' && (
                          <button
                            onClick={async (e) => {
                              e.stopPropagation();
                              if (confirm('确定要删除这个素材吗？')) {
                                await deleteUserMaterial(image.materialId!);
                              }
                            }}
                            className="absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 shadow-lg z-10"
                            title="删除素材"
                          >
                            ×
                          </button>
                        )}
                            </div>
                          ))}
                        </div>
                        
                        {/* 素材统计信息 */}
                        <div className="mt-3 space-y-2">
                          {/* 测试图片说明 */}
                          {uploadedImages.some(img => img.id === 'test-image-001') && (
                            <div className="p-2 bg-orange-50 border border-orange-200 rounded-lg">
                              <div className="text-xs text-orange-700 text-center">
                                🧪 <span className="font-medium">测试图片</span>：用于前端功能测试，可直接拖拽到键帽上进行设计体验
                              </div>
                            </div>
                          )}

                          <div className="p-2 bg-gray-50 rounded-lg">
                            <div className="text-xs text-gray-600 text-center">
                              💡 <span className="text-blue-600">创建图层</span>：可拖拽调整的贴图层 | <span className="text-green-600">应用键帽</span>：设为所有键帽背景
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
                
                {/* 素材展示模式 - 显示公共素材 */}
                {materialLibraryView === 'materials' && (
                  <>
                    {filteredMaterials.length === 0 ? (
                      <div className="text-center py-6">
                        <div className="text-4xl mb-3">📦</div>
                        <p className="text-sm text-gray-500 mb-2">
                          暂无此分类的素材
                        </p>
                        <p className="text-xs text-gray-400 mb-3">
                          请选择其他分类或稍后再试
                        </p>
                        

                        
                        <div className="space-y-2">
                          <button
                            onClick={() => {
                              console.log('🔄 手动刷新素材');
                              loadPublicMaterials();
                            }}
                            className="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors mr-2"
                          >
                            刷新素材
                          </button>
                          <button
                            onClick={backToCategories}
                            className="px-3 py-2 bg-gray-600 text-white text-sm rounded-lg hover:bg-gray-700 transition-colors"
                          >
                            返回分类选择
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {/* 公共素材网格布局 */}
                        <div className="grid grid-cols-2 gap-2">
                          {filteredMaterials.map((material) => (
                            <div
                              key={material.id}
                              className="relative group rounded-lg overflow-hidden transition-all duration-200 hover:shadow-sm border border-gray-200 hover:border-gray-300"
                            >
                              <img
                                src={material.url}
                                alt={material.name}
                                className="w-full h-20 object-cover pointer-events-none select-none"
                                draggable={false}
                              />
                              <div className="p-2">
                                <p className="text-xs text-gray-600 truncate font-medium">{material.name}</p>
                                {/* 显示图片信息 */}
                                {material.width && material.height && (
                                  <p className="text-xs text-gray-400 mt-1">
                                    {material.width}×{material.height}
                                    {material.fileSize && ` · ${Math.round(material.fileSize / 1024)}KB`}
                                  </p>
                                )}
                                
                                {/* 应用按钮 */}
                                <div className="mt-2 flex flex-col gap-1">
                                  {/* 第一行：创建图层和应用键帽 */}
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() => applyMaterialToKeyboard(material)}
                                      className="flex-1 bg-blue-500 text-white text-xs px-2 py-1 rounded hover:bg-blue-600 transition-colors"
                                    >
                                      创建图层
                                    </button>
                                    <button
                                      onClick={() => applyMaterialToKeycaps(material)}
                                      className="flex-1 bg-green-500 text-white text-xs px-2 py-1 rounded hover:bg-green-600 transition-colors"
                                    >
                                      应用键帽
                                    </button>
                                  </div>
                                  {/* 第二行：覆盖 */}
                                  <button
                                    onClick={() => applyMaterialToKeyboardWithExtension(material)}
                                    className="w-full bg-purple-500 text-white text-xs px-2 py-1 rounded hover:bg-purple-600 transition-colors"
                                    title="创建图层并相对键帽间距扩展9.1mm (11.4倍键帽间距0.8mm)"
                                  >
                                    覆盖 (+9.1mm)
                                  </button>
                                </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* 素材统计信息 */}
                  <div className="mt-3 p-2 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 text-center">
                            💡 <span className="text-blue-600">创建图层</span>：可拖拽调整的贴图层 | <span className="text-green-600">应用键帽</span>：设为所有键帽背景 | <span className="text-purple-600">扩展覆盖</span>：相对键帽间距扩展9.1mm
                    </div>
                  </div>
                  </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 中间设计画布 */}
        <div className="flex-1 bg-gray-100 relative">
          <div className="absolute inset-4">
            <div 
              ref={designAreaRef}
              className="w-full h-full bg-white rounded-lg overflow-hidden transition-all duration-200"
              onClick={(e) => {
                // 点击空白区域取消选中拖拽元素
                const target = e.target as HTMLElement;
                // 检查点击的是否是键帽相关元素
                const isKeycapClick = target.closest('.keycap-group') || 
                                    target.getAttribute('data-key') || 
                                    target.classList.contains('keycap-top') ||
                                    (target.tagName === 'text' && target.closest('[data-key]'));
                
                // 如果不是键帽点击，且点击的是空白区域，则取消选中拖拽元素
                if (!isKeycapClick && (target === designAreaRef.current || target.tagName === 'svg')) {
                  setSelectedElement(null);
                }
              }}
            >
              {/* 视图切换按钮 */}
              <div className="absolute top-2 left-2 z-50 flex space-x-2">
                <button
                  onClick={() => setRenderMode('2d')}
                  className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                    renderMode === '2d' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  2D视图
                </button>
                <button
                  onClick={() => setRenderMode('3d')}
                  className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                    renderMode === '3d' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  3D视图
                </button>
              </div>

              {/* 统一画布层 - 使用相同的padding确保坐标一致 */}
              <div className="absolute inset-0 p-4">
                {/* 键盘背景层 - 2D或3D渲染 */}
                <div className="absolute inset-0 z-10">
                  {renderMode === '2d' ? (
                    // 2D SVG渲染
                    <svg 
              viewBox="0 0 1350 420" 
              className="w-full h-full" 
              style={{ 
                pointerEvents: 'auto',
                background: '#f9f9f9'
              }}
            >
                      <defs>
                        {/* 键帽边框样式 */}
                        <linearGradient id="keycapBorder" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" style={{stopColor: '#8e9aaf', stopOpacity: 0.8}} />
                          <stop offset="100%" style={{stopColor: '#6c757d', stopOpacity: 0.6}} />
                        </linearGradient>
                        
                        {/* 键帽高光效果 */}
                        <linearGradient id="keycapHighlight" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" style={{stopColor: '#ffffff', stopOpacity: 0.6}} />
                          <stop offset="100%" style={{stopColor: '#ffffff', stopOpacity: 0.1}} />
                        </linearGradient>
                        
                        {/* 创建一个遮罩，只在键帽顶部区域显示贴图 */}
                        <mask id="keycapsMask">
                          {/* 背景为黑色（不显示） */}
                          <rect x="0" y="0" width="1350" height="420" fill="black" />
                          {/* 键帽顶部区域为白色（显示） */}
                          {renderKeyboardLayoutMask(designSettings.layout)}
                        </mask>
                      </defs>


                      {/* 渲染拖拽的贴图元素，使用遮罩让其只在键帽区域显示 */}
                      {draggedElements.map((element) => (
                        <image
                          key={element.id}
                          x={element.x}
                          y={element.y}
                          width={element.width}
                          height={element.height}
                          href={element.imageUrl}
                          opacity={element.opacity}
                          preserveAspectRatio="none"
                          transform={`rotate(${element.rotation} ${element.x + element.width/2} ${element.y + element.height/2})`}
                          mask="url(#keycapsMask)"
                          style={{ pointerEvents: 'none' }}
                        />
                      ))}
                      
                      {/* 动态渲染选择的键盘布局 - 键帽可以点击 */}
                      <g style={{ pointerEvents: 'auto' }}>
                        {renderKeyboardLayout(designSettings.layout)}
                      </g>
                    </svg>
                  ) : (
                    // 3D Three.js渲染
                    <div className="w-full h-full bg-gray-100 relative">
                      <ThreeJSKeyboard
                        layoutId={designSettings.layout}
                        selectedKeys={selectedKeys}
                        onKeyClick={handleKeyClick}
                        designElements={draggedElements}
                        keycapCustomizations={keycapCustomizations}
                        className="w-full h-full"
                      />
                      
                      {/* 调试信息覆盖层 */}
                      <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded z-50">
                        3D渲染模式 | 布局: {designSettings.layout}
                      </div>
                    </div>
                  )}
                </div>

                {/* 拖拽元素控制层 - 在SVG外部处理交互 */}
                <div 
                  className="absolute inset-0 z-20 pointer-events-none"
                  style={{
                    // 全局性能优化
                    willChange: 'auto', // 让浏览器自动决定
                    contain: 'layout style paint', // 限制重排重绘范围
                  }}
                >
                  {/* 渲染拖拽元素的控制界面 */}
                  {draggedElements.map((element) => {
                    // 获取设计区域的实际尺寸
                    const designArea = designAreaRef.current;
                    if (!designArea) return null;
                    
                    const designRect = designArea.getBoundingClientRect();
                    
                    // SVG的原始比例
                    const svgWidth = 1350;
                    const svgHeight = 420;
                    const svgAspectRatio = svgWidth / svgHeight; // ≈ 3.214
                    
                    // 设计区域的实际比例
                    const areaAspectRatio = designRect.width / designRect.height;
                    
                    // 计算 SVG 在设计区域中的实际显示尺寸
                    let actualSvgWidth, actualSvgHeight, offsetX = 0, offsetY = 0;
                    
                    if (areaAspectRatio > svgAspectRatio) {
                      // 设计区域更宽，SVG会被高度限制
                      actualSvgHeight = designRect.height;
                      actualSvgWidth = actualSvgHeight * svgAspectRatio;
                      offsetX = (designRect.width - actualSvgWidth) / 2;
                    } else {
                      // 设计区域更高，SVG会被宽度限制
                      actualSvgWidth = designRect.width;
                      actualSvgHeight = actualSvgWidth / svgAspectRatio;
                      offsetY = (designRect.height - actualSvgHeight) / 2;
                    }
                    
                    // 计算元素在实际SVG区域中的屏幕坐标
                    const scaleX = actualSvgWidth / svgWidth;
                    const scaleY = actualSvgHeight / svgHeight;
                    
                    const screenLeft = element.x * scaleX + offsetX;
                    const screenTop = element.y * scaleY + offsetY;
                    const screenWidth = element.width * scaleX;
                    const screenHeight = element.height * scaleY;
                    
                    // 选择框的边距（比图片稍大一点）
                    const borderPadding = 4; // 4像素边距
                    
                    return (
                      <div
                        key={element.id}
                        className="absolute pointer-events-auto"
                        style={{
                          left: `${screenLeft - borderPadding}px`,
                          top: `${screenTop - borderPadding}px`,
                          width: `${screenWidth + borderPadding * 2}px`,
                          height: `${screenHeight + borderPadding * 2}px`,
                          // 性能优化：启用硬件加速
                          willChange: 'transform',
                          transform: 'translateZ(0)', // 强制创建复合层
                          backfaceVisibility: 'hidden', // 避免背面可见导致的性能问题
                        }}
                      >
                        {/* 选择边框 - 紧贴图片但稍微大一点 */}
                        {selectedElement === element.id && (
                          <div 
                            className="absolute inset-0 border-2 border-blue-500 border-dashed pointer-events-none"
                            style={{
                              backgroundColor: 'rgba(59, 130, 246, 0.1)',
                              borderRadius: '2px',
                            }}
                          />
                        )}
                        
                        {/* 透明的交互层 - 覆盖整个选择框区域 */}
                        <div
                          className="absolute inset-0 cursor-move"
                          style={{
                            // 性能优化：确保快速响应
                            touchAction: 'none', // 禁用触摸滚动，避免延迟
                            userSelect: 'none', // 禁用文本选择
                            WebkitUserSelect: 'none', // Safari 兼容
                            MozUserSelect: 'none', // Firefox 兼容
                            msUserSelect: 'none', // IE 兼容
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setSelectedElement(element.id);
                            setEditingMode('image');
                            setSelectedKeycap(null);
                            handleElementDrag(element.id, e);
                          }}
                        />
                        
                        {/* 控制按钮和手柄 */}
                        {selectedElement === element.id && (
                          <>
                            {/* 删除按钮 */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setDraggedElements(prev => prev.filter(el => el.id !== element.id));
                                if (selectedElement === element.id) {
                                  setSelectedElement(null);
                                }
                              }}
                              className="absolute -top-3 -right-3 w-6 h-6 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600 transition-colors shadow-lg z-10"
                            >
                              ×
                            </button>
                        
                            {/* 四角调整大小手柄 */}
                            <div 
                              className="absolute -top-2 -left-2 w-4 h-4 bg-blue-500 rounded-full cursor-nw-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'nw');
                              }}
                            />
                            <div 
                              className="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 rounded-full cursor-ne-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'ne');
                              }}
                            />
                            <div 
                              className="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500 rounded-full cursor-sw-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'sw');
                              }}
                            />
                            <div 
                              className="absolute -bottom-2 -right-2 w-4 h-4 bg-blue-500 rounded-full cursor-se-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'se');
                              }}
                            />
                            
                            {/* 边缘调整大小手柄 - 用于单独调整宽度或高度 */}
                            {/* 上边缘 - 只调整高度 */}
                            <div 
                              className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full cursor-n-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'n');
                              }}
                            />
                            {/* 下边缘 - 只调整高度 */}
                            <div 
                              className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full cursor-s-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 's');
                              }}
                            />
                            {/* 左边缘 - 只调整宽度 */}
                            <div 
                              className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-green-500 rounded-full cursor-w-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'w');
                              }}
                            />
                            {/* 右边缘 - 只调整宽度 */}
                            <div 
                              className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-green-500 rounded-full cursor-e-resize border-2 border-white shadow-lg z-10"
                              style={{
                                touchAction: 'none',
                                userSelect: 'none',
                                willChange: 'transform',
                              }}
                              onMouseDown={(e) => {
                                e.stopPropagation();
                                handleElementResize(element.id, e, 'e');
                              }}
                            />
                            
                            {/* 元素信息提示 */}
                            <div className="absolute -top-8 left-0 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-10 whitespace-nowrap">
                              {Math.round(element.width)}×{Math.round(element.height)} | 比例: {(element.width / element.height).toFixed(2)}
                            </div>
                          </>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 引导层 */}
              <div className="absolute inset-0 z-40 pointer-events-none">
                {/* 无素材时的引导界面 */}
                {uploadedImages.length === 0 && showWelcome && (
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                    <div className="bg-white bg-opacity-95 rounded-lg p-8 shadow-xl pointer-events-auto">
                      <div className="text-6xl mb-4">📁</div>
                      <h3 className="text-xl font-semibold text-gray-800 mb-3">欢迎使用键帽设计器</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        键盘已设置为透明3D效果，适合图片拖拽设计
                      </p>
                      <div className="text-left space-y-2 text-sm text-gray-600 mb-4">
                        <div className="flex items-center space-x-2">
                          <span>1️⃣</span>
                          <span>点击左侧 📁 按钮上传设计素材</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span>2️⃣</span>
                          <span>拖拽图片到透明键帽上进行设计</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span>3️⃣</span>
                          <span>点击键帽选择并应用设计</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <button
                          onClick={() => !isUploading && fileInputRef.current?.click()}
                          disabled={isUploading}
                          className={`px-4 py-2 rounded-lg transition-colors text-sm ${
                            isUploading
                              ? 'bg-gray-400 text-gray-100 cursor-not-allowed'
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isUploading ? '⏳ 上传中...' : '开始上传素材'}
                        </button>
                        <button
                          onClick={() => setShowWelcome(false)}
                          className="block mx-auto text-xs text-gray-500 hover:text-gray-700"
                        >
                          跳过引导
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 使用提示（当没有拖拽元素时显示） */}
                {draggedElements.length === 0 && uploadedImages.length > 0 && showGuide && (
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                    <div className="bg-white bg-opacity-95 rounded-lg p-6 shadow-xl animate-pulse pointer-events-auto">
                      <div className="text-4xl mb-3">🎨</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">开始设计您的键帽</h3>
                      <p className="text-sm text-gray-600 mb-2">从左侧素材区拖拽图片到这里</p>
                      <div className="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-3">
                        <span>🖱️ 拖拽图片</span>
                        <span>🎯 点击键帽选择</span>
                        <span>✨ 透明3D效果</span>
                      </div>
                      
                      {/* 拖拽区域指示 */}
                      <div className="border-2 border-dashed border-blue-300 rounded-lg p-4 bg-blue-50 bg-opacity-50">
                        <div className="text-blue-600 text-sm font-medium mb-1">拖拽区域</div>
                        <div className="text-blue-500 text-xs">将图片拖拽到此区域或键盘上任意位置</div>
                      </div>
                      
                      {/* 3秒倒计时提示 */}
                      <div className="mt-3 text-xs text-gray-400">
                        提示将在 3 秒后自动消失
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 拖拽悬停提示已移除 - 使用新的自定义拖拽体验 */}
            </div>
          </div>
        </div>

        {/* 超高性能拖拽预览 - 直接DOM操作，零延迟 */}
        <UltraFastDragPreview />
        
        {/* 兼容性预览（已禁用，使用DOM版本） */}
        {false && dragPreview?.isActive && (
          <DragPreviewComponent dragPreview={dragPreview} />
        )}

        {/* 右侧设置面板 */}
        <div className="w-64 bg-white border-l border-gray-200 overflow-y-auto h-full flex flex-col">
          <div className="p-3 flex-1">
            <h3 className="font-medium text-gray-900 mb-3">设置</h3>
            
            <div className="space-y-3">
              {/* 设计名称 */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">设计名称</label>
                <input
                  type="text"
                  value={designName}
                  onChange={(e) => setDesignName(e.target.value)}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  placeholder="输入设计名称"
                />
              </div>

              {/* 画布尺寸 */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">画布尺寸</label>
                <div className="flex items-center space-x-1">
                  <input
                    type="number"
                    value={designSettings.customWidth}
                    onChange={(e) => setDesignSettings(prev => ({ ...prev, customWidth: parseInt(e.target.value) || 1305 }))}
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                    placeholder="宽"
                  />
                  <span className="text-gray-500 text-xs">×</span>
                  <input
                    type="number"
                    value={designSettings.customHeight}
                    onChange={(e) => setDesignSettings(prev => ({ ...prev, customHeight: parseInt(e.target.value) || 395 }))}
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                    placeholder="高"
                  />
                </div>
              </div>



              {/* 键盘布局 */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">键盘布局</label>
                <select
                  value={designSettings.layout}
                  onChange={(e) => {
                    setDesignSettings(prev => ({ ...prev, layout: e.target.value }));
                  }}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                >
                  {KEYBOARD_LAYOUTS.map((layout) => (
                    <option key={layout.id} value={layout.id}>
                      {layout.name} ({layout.keys}键)
                    </option>
                  ))}
                </select>
                
                {/* 调试信息显示 */}
                <div className="mt-1 p-1 bg-gray-50 rounded text-xs text-gray-600">
                  当前布局: {designSettings.layout} - {KEYBOARD_LAYOUTS.find(l => l.id === designSettings.layout)?.name}
                </div>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              {/* 上传状态显示 */}
              {isUploading && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin text-blue-600">⏳</div>
                    <div>
                      <div className="text-sm font-medium text-blue-800">正在上传图片...</div>
                      <div className="text-xs text-blue-600">请稍候，图片正在上传到服务器</div>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="p-2 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 text-center">
                  使用左侧工具栏的 💾 保存设计，👁️ 进行3D预览
                </p>
              </div>
              
              {/* 引导控制 */}
              <div className="p-2 bg-gray-50 rounded-lg">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowWelcome(true)}
                    className="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    disabled={uploadedImages.length === 0}
                  >
                    重置引导
                  </button>
                  <button
                    onClick={() => setShowGuide(true)}
                    className="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    disabled={uploadedImages.length === 0 || draggedElements.length > 0}
                  >
                    显示提示
                  </button>
                </div>
              </div>
              
              {/* 选中键帽信息 */}
              {selectedKeys.length > 0 && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-700 mb-1">选中的键帽</h4>
                  <p className="text-xs text-gray-600 mb-2">
                    已选中 {selectedKeys.length} 个键帽
                  </p>
                  
                  {/* 显示选中的键帽列表 */}
                  <div className="mb-2 max-h-20 overflow-y-auto">
                    <div className="flex flex-wrap gap-1">
                      {selectedKeys.slice(0, 10).map(keyId => (
                        <span
                          key={keyId}
                          className="inline-block px-1 py-0.5 bg-blue-100 text-blue-700 text-xs rounded border"
                        >
                          {keyId.replace('key-', '').replace('-', ' ').toUpperCase()}
                        </span>
                      ))}
                      {selectedKeys.length > 10 && (
                        <span className="text-xs text-gray-500">
                          +{selectedKeys.length - 10} 更多...
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <button
                      onClick={() => setSelectedKeys([])}
                      className="w-full px-2 py-1 text-xs bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      清除所有选择
                    </button>
                    <button
                      className="w-full px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      应用设计到选中键帽
                    </button>
                  </div>
                  
                  {/* 提示信息 */}
                  <div className="mt-2 p-2 bg-blue-100 rounded text-xs text-blue-700">
                    💡 点击键帽可切换选中状态
                  </div>
                </div>
              )}
              
              {/* 键帽选择测试 */}
              {selectedKeys.length === 0 && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">键帽选择功能</h4>
                  <p className="text-xs text-gray-600 mb-2">
                    点击键帽进行选择，被选中的键帽会显示蓝色高亮
                  </p>
                  
                  {/* 测试按钮 */}
                  <div className="space-y-2">
                    <button
                      onClick={() => setSelectedKeys(['key-space', 'key-enter', 'key-esc'])}
                      className="w-full px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      🧪 测试选择 (SPACE/ENTER/ESC)
                    </button>
                    
                    {/* 手动触发键帽点击测试 */}
                    <button
                      onClick={() => {
                        console.log('🔧 手动触发键帽点击测试');
                        handleKeyClick('key-a');
                      }}
                      className="w-full px-2 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                      🔧 手动测试 A 键点击
                    </button>
                    
                    {/* 清除console */}
                    <button
                      onClick={() => {
                        console.clear();
                        console.log('🧹 控制台已清空 - 开始新的测试');
                      }}
                      className="w-full px-2 py-1 text-xs bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                    >
                      🧹 清空调试日志
                    </button>
                  </div>
                  
                  <div className="mt-2 p-2 bg-yellow-50 rounded text-xs text-yellow-700">
                    ⚡ 打开浏览器控制台(F12)查看调试信息
                  </div>
                  
                  <div className="mt-1 p-2 bg-blue-50 rounded text-xs text-blue-700">
                    🎯 鼠标悬停键帽时应该在控制台看到日志
                  </div>
                </div>
              )}

              {/* 键帽编辑控制面板 */}
              {selectedKeycap && editingMode === 'keycap' && (
                <div className="p-3 bg-orange-50 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-700 mb-2 flex items-center">
                    <span className="mr-2">🎨</span>
                    键帽编辑 - {selectedKeycap.replace('key-', '').replace('-', ' ').toUpperCase()}
                  </h4>
                  
                  {(() => {
                    const customization = getKeycapCustomization(selectedKeycap);
                    
                    return (
                      <div className="space-y-3">
                        {/* 字体设置 */}
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-700">字体样式</div>
                          
                          {/* 字体颜色 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">字体颜色</label>
                            <div className="flex items-center space-x-2">
                              <input
                                type="color"
                                value={customization.textColor}
                                onChange={(e) => updateKeycapCustomization(selectedKeycap, { textColor: e.target.value })}
                                className="w-8 h-6 border border-gray-300 rounded"
                              />
                              <input
                                type="text"
                                value={customization.textColor}
                                onChange={(e) => updateKeycapCustomization(selectedKeycap, { textColor: e.target.value })}
                                className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded"
                                placeholder="#000000"
                              />
                            </div>
                          </div>
                          
                          {/* 字体大小 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">
                              字体大小: {customization.fontSize}px
                            </label>
                            <input
                              type="range"
                              min="8"
                              max="24"
                              step="1"
                              value={customization.fontSize}
                              onChange={(e) => updateKeycapCustomization(selectedKeycap, { fontSize: parseInt(e.target.value) })}
                              className="w-full range-slider"
                            />
                          </div>
                          
                          {/* 字体族 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">字体族</label>
                            <select
                              value={customization.fontFamily}
                              onChange={(e) => updateKeycapCustomization(selectedKeycap, { fontFamily: e.target.value })}
                              className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                            >
                              <option value="Arial, sans-serif">Arial</option>
                              <option value="Helvetica, sans-serif">Helvetica</option>
                              <option value="Times, serif">Times</option>
                              <option value="Courier, monospace">Courier</option>
                              <option value="Verdana, sans-serif">Verdana</option>
                              <option value="Georgia, serif">Georgia</option>
                              <option value="Comic Sans MS, cursive">Comic Sans</option>
                              <option value="Impact, fantasy">Impact</option>
                            </select>
                          </div>
                          
                          {/* 字体粗细 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">字体粗细</label>
                            <select
                              value={customization.fontWeight}
                              onChange={(e) => updateKeycapCustomization(selectedKeycap, { fontWeight: e.target.value })}
                              className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                            >
                              <option value="normal">正常</option>
                              <option value="bold">粗体</option>
                              <option value="lighter">细体</option>
                              <option value="bolder">特粗</option>
                              <option value="100">100</option>
                              <option value="200">200</option>
                              <option value="300">300</option>
                              <option value="400">400</option>
                              <option value="500">500</option>
                              <option value="600">600</option>
                              <option value="700">700</option>
                              <option value="800">800</option>
                              <option value="900">900</option>
                            </select>
                          </div>
                          
                          {/* 字体样式 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">字体样式</label>
                            <div className="grid grid-cols-3 gap-1">
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textStyle: 'normal' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textStyle === 'normal' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                正常
                              </button>
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textStyle: 'italic' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textStyle === 'italic' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                斜体
                              </button>
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textStyle: 'oblique' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textStyle === 'oblique' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                倾斜
                              </button>
                            </div>
                          </div>
                          
                          {/* 文字装饰 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">文字装饰</label>
                            <div className="grid grid-cols-3 gap-1">
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textDecoration: 'none' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textDecoration === 'none' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                无
                              </button>
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textDecoration: 'underline' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textDecoration === 'underline' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                下划线
                              </button>
                              <button
                                onClick={() => updateKeycapCustomization(selectedKeycap, { textDecoration: 'line-through' })}
                                className={`px-2 py-1 text-xs border rounded transition-colors ${
                                  customization.textDecoration === 'line-through' 
                                    ? 'bg-blue-500 text-white border-blue-500' 
                                    : 'bg-white border-gray-300 hover:bg-gray-50'
                                }`}
                              >
                                删除线
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* 背景贴图设置 */}
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-700">背景贴图</div>
                          
                          {customization.backgroundImage ? (
                            <div className="space-y-2">
                              {/* 贴图预览 */}
                              <div className="flex items-center space-x-2">
                                <img
                                  src={customization.backgroundImage}
                                  alt="背景贴图"
                                  className="w-8 h-8 object-cover rounded border"
                                />
                                <div className="flex-1 text-xs text-gray-600">
                                  已应用贴图
                                </div>
                                <button
                                  onClick={() => removeKeycapImage(selectedKeycap)}
                                  className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                >
                                  移除
                                </button>
                              </div>

                              {/* 覆盖模式选择 */}
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">覆盖模式</label>
                                <div className="grid grid-cols-2 gap-2">
                                  <button
                                    onClick={() => updateKeycapCustomization(selectedKeycap, { coverageMode: 'full-keycap' })}
                                    className={`px-2 py-1 text-xs rounded border transition-colors ${
                                      customization.coverageMode === 'full-keycap'
                                        ? 'bg-blue-500 text-white border-blue-500'
                                        : 'bg-white border-gray-300 hover:bg-gray-50'
                                    }`}
                                  >
                                    整个键帽
                                  </button>
                                  <button
                                    onClick={() => updateKeycapCustomization(selectedKeycap, { coverageMode: 'top-only' })}
                                    className={`px-2 py-1 text-xs rounded border transition-colors ${
                                      customization.coverageMode === 'top-only'
                                        ? 'bg-blue-500 text-white border-blue-500'
                                        : 'bg-white border-gray-300 hover:bg-gray-50'
                                    }`}
                                  >
                                    仅顶面
                                  </button>
                                </div>
                              </div>

                              {/* 贴图透明度 */}
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">
                                  贴图透明度: {Math.round(customization.backgroundOpacity * 100)}%
                                </label>
                                <input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.01"
                                  value={customization.backgroundOpacity}
                                  onChange={(e) => updateKeycapCustomization(selectedKeycap, { backgroundOpacity: parseFloat(e.target.value) })}
                                  className="w-full range-slider"
                                />
                              </div>
                              
                              {/* 贴图缩放 */}
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">
                                  贴图缩放: {Math.round(customization.backgroundScale * 100)}%
                                </label>
                                <input
                                  type="range"
                                  min="0.1"
                                  max="3"
                                  step="0.1"
                                  value={customization.backgroundScale}
                                  onChange={(e) => updateKeycapCustomization(selectedKeycap, { backgroundScale: parseFloat(e.target.value) })}
                                  className="w-full range-slider"
                                />
                              </div>
                              
                              {/* 贴图位置 */}
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">贴图位置</label>
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <label className="block text-xs text-gray-500">X偏移</label>
                                    <input
                                      type="range"
                                      min="-20"
                                      max="20"
                                      step="1"
                                      value={customization.backgroundPosition.x}
                                      onChange={(e) => updateKeycapCustomization(selectedKeycap, { 
                                        backgroundPosition: { 
                                          ...customization.backgroundPosition, 
                                          x: parseInt(e.target.value) 
                                        } 
                                      })}
                                      className="w-full range-slider"
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs text-gray-500">Y偏移</label>
                                    <input
                                      type="range"
                                      min="-20"
                                      max="20"
                                      step="1"
                                      value={customization.backgroundPosition.y}
                                      onChange={(e) => updateKeycapCustomization(selectedKeycap, { 
                                        backgroundPosition: { 
                                          ...customization.backgroundPosition, 
                                          y: parseInt(e.target.value) 
                                        } 
                                      })}
                                      className="w-full range-slider"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 border-2 border-dashed border-gray-300 rounded-lg text-center">
                              <div className="text-xs text-gray-500 mb-2">
                                拖拽图片到键帽上应用背景贴图
                              </div>
                              <div className="text-xs text-gray-400">
                                或从左侧素材库拖拽图片
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {/* 操作按钮 */}
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedKeycap(null);
                              setEditingMode(null);
                            }}
                            className="flex-1 px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                          >
                            完成编辑
                          </button>
                          <button
                            onClick={() => {
                              // 重置键帽样式
                              setKeycapCustomizations(prev => {
                                const newCustomizations = { ...prev };
                                delete newCustomizations[selectedKeycap];
                                return newCustomizations;
                              });
                            }}
                            className="flex-1 px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                          >
                            重置样式
                          </button>
                        </div>
                        
                        {/* 提示信息 */}
                        <div className="mt-2 p-2 bg-orange-100 rounded text-xs text-orange-700">
                          💡 实时预览：修改会立即显示在键帽上
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* 图片编辑控制面板 */}
              {selectedElement && editingMode === 'image' && (
                <div className="p-3 bg-green-50 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">图片编辑</h4>
                  
                  {(() => {
                    const element = draggedElements.find(el => el.id === selectedElement);
                    if (!element) return null;
                    
                    return (
                      <div className="space-y-3">
                        {/* 快速操作按钮 */}
                        <div className="grid grid-cols-2 gap-2">
                          <button
                            onClick={() => fitToKeyboard(selectedElement)}
                            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                          >
                            适配键盘
                          </button>
                          <button
                            onClick={() => tileToKeyboard(selectedElement)}
                            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                          >
                            平铺整个
                          </button>
                        </div>
                        
                        {/* 透明度控制 */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            透明度: {Math.round(element.opacity * 100)}%
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.01"
                            value={element.opacity}
                            onChange={(e) => updateElementProperty(selectedElement, 'opacity', parseFloat(e.target.value))}
                            className="w-full range-slider"
                          />
                        </div>
                        
                        {/* 圆角控制 */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            圆角: {element.borderRadius}px
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="20"
                            step="1"
                            value={element.borderRadius}
                            onChange={(e) => updateElementProperty(selectedElement, 'borderRadius', parseInt(e.target.value))}
                            className="w-full range-slider"
                          />
                        </div>
                        
                        {/* 旋转控制 */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            旋转: {element.rotation}°
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="360"
                            step="1"
                            value={element.rotation}
                            onChange={(e) => updateElementProperty(selectedElement, 'rotation', parseInt(e.target.value))}
                            className="w-full range-slider"
                          />
                        </div>
                        
                        {/* 位置和大小信息 */}
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <label className="block text-gray-600">位置 X</label>
                            <input
                              type="number"
                              value={Math.round(element.x)}
                              onChange={(e) => updateElementProperty(selectedElement, 'x', parseInt(e.target.value) || 0)}
                              className="w-full px-1 py-0.5 border border-gray-300 rounded text-xs"
                            />
                          </div>
                          <div>
                            <label className="block text-gray-600">位置 Y</label>
                            <input
                              type="number"
                              value={Math.round(element.y)}
                              onChange={(e) => updateElementProperty(selectedElement, 'y', parseInt(e.target.value) || 0)}
                              className="w-full px-1 py-0.5 border border-gray-300 rounded text-xs"
                            />
                          </div>
                          <div>
                            <label className="block text-gray-600">宽度</label>
                            <input
                              type="number"
                              value={Math.round(element.width)}
                              onChange={(e) => updateElementProperty(selectedElement, 'width', parseInt(e.target.value) || 20)}
                              className="w-full px-1 py-0.5 border border-gray-300 rounded text-xs"
                            />
                          </div>
                          <div>
                            <label className="block text-gray-600">高度</label>
                            <input
                              type="number"
                              value={Math.round(element.height)}
                              onChange={(e) => updateElementProperty(selectedElement, 'height', parseInt(e.target.value) || 20)}
                              className="w-full px-1 py-0.5 border border-gray-300 rounded text-xs"
                            />
                          </div>
                        </div>
                        
                        {/* 图层操作 */}
                        <div className="grid grid-cols-4 gap-1">
                          <button className="px-1 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="置于顶层">
                            🔝
                          </button>
                          <button className="px-1 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="上移一层">
                            ⬆️
                          </button>
                          <button className="px-1 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="下移一层">
                            ⬇️
                          </button>
                          <button className="px-1 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors" title="置于底层">
                            🔻
                          </button>
                        </div>
                        
                        {/* 提示信息 */}
                        <div className="mt-2 p-2 bg-green-100 rounded text-xs text-green-700">
                          💡 蓝色圆点：调整整体大小 | 绿色圆点：单独调整宽度或高度（拉伸）
                        </div>
                        
                        {/* 快捷键提示 */}
                        <div className="mt-1 p-2 bg-gray-100 rounded text-xs text-gray-600">
                          🔑 快捷键: Delete删除 | Esc取消选中
                        </div>
                        
                        {/* 调试信息 */}
                        <div className="mt-1 p-2 bg-yellow-50 rounded text-xs text-yellow-700">
                          📊 ID: {element.id.slice(-6)} | 层级: {selectedElement === element.id ? 30 : 25}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
            
            {/* 全局状态信息 */}
            <div className="mt-4 p-2 bg-gray-50 rounded-lg text-xs text-gray-600">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>已上传素材:</span>
                  <span className="font-medium">{uploadedImages.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>设计元素:</span>
                  <span className="font-medium">{draggedElements.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>选中键帽:</span>
                  <span className="font-medium">{selectedKeys.length}</span>
                </div>
                {editingMode && (
                  <div className="flex justify-between">
                    <span>当前编辑:</span>
                    <span className={`font-medium ${editingMode === 'image' ? 'text-green-600' : 'text-orange-600'}`}>
                      {editingMode === 'image' ? '图片元素' : '键帽样式'}
                    </span>
                  </div>
                )}
                {selectedKeycap && (
                  <div className="flex justify-between">
                    <span>编辑键帽:</span>
                    <span className="font-medium text-orange-600">
                      {selectedKeycap.replace('key-', '').replace('-', ' ').toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 